// renderer.js

// --- 1. Get references to UI elements ---
const refineBtn = document.getElementById('refine-btn');
const copyBtn = document.getElementById('copy-btn');
const clearBtn = document.getElementById('clear-btn');
const settingsBtn = document.getElementById('settings-btn');
const inputText = document.getElementById('input-text');
const outputText = document.getElementById('output-text');
const langSelect = document.getElementById('lang-select');
const toneSelect = document.getElementById('tone-select');
const actionSelect = document.getElementById('action-select');
const charCount = document.getElementById('char-count');
const loadingOverlay = document.querySelector('.loading-overlay');
const statusDot = document.querySelector('.status-dot');
const statusText = document.querySelector('.status-text');
const copyIcon = document.querySelector('.copy-icon');
const copyText = document.querySelector('.copy-text');

// Settings Modal Elements
const settingsModal = document.getElementById('settings-modal');
const closeSettingsBtn = document.getElementById('close-settings-btn');
const providerSelect = document.getElementById('provider-select');
const modelSelect = document.getElementById('model-select');
const refreshModelsBtn = document.getElementById('refresh-models-btn');
const openrouterSettings = document.getElementById('openrouter-settings');
const apiKeyInput = document.getElementById('api-key-input');
const toggleKeyVisibility = document.getElementById('toggle-key-visibility');
const autoSaveSettings = document.getElementById('auto-save-settings');
const showCharCount = document.getElementById('show-char-count');
const rememberLastText = document.getElementById('remember-last-text');
const resetSettingsBtn = document.getElementById('reset-settings-btn');
const saveSettingsBtn = document.getElementById('save-settings-btn');

// Default preference elements
const defaultActionSelect = document.getElementById('default-action-select');
const defaultToneSelect = document.getElementById('default-tone-select');
const defaultLanguageSelect = document.getElementById('default-language-select');

// Model default elements
const setDefaultModelBtn = document.getElementById('set-default-model-btn');
const defaultModelIndicator = document.getElementById('default-model-indicator');

// Dynamic UI Elements
const aiSuggestionTitle = document.getElementById('ai-suggestion-title');
const loadingText = document.getElementById('loading-text');
const outputTextarea = document.getElementById('output-text');

// Custom Prompts Elements
const customPromptsSection = document.getElementById('custom-prompts-section');
const savedPromptsSelect = document.getElementById('saved-prompts-select');
const customPromptInput = document.getElementById('custom-prompt-input');
const newPromptBtn = document.getElementById('new-prompt-btn');
const managePromptsBtn = document.getElementById('manage-prompts-btn');

// Prompts Management Modal Elements
const promptsModal = document.getElementById('prompts-modal');
const closePromptsBtn = document.getElementById('close-prompts-btn');
const promptsList = document.getElementById('prompts-list');
const emptyPromptsMessage = document.getElementById('empty-prompts-message');
const promptNameInput = document.getElementById('prompt-name-input');
const promptCategorySelect = document.getElementById('prompt-category-select');
const promptTextInput = document.getElementById('prompt-text-input');
const promptDescriptionInput = document.getElementById('prompt-description-input');
const deletePromptBtn = document.getElementById('delete-prompt-btn');
const cancelPromptBtn = document.getElementById('cancel-prompt-btn');
const savePromptBtn = document.getElementById('save-prompt-btn');

// Batch Processing Elements
const batchProcessBtn = document.getElementById('batch-process-btn');
const batchModal = document.getElementById('batch-modal');
const closeBatchBtn = document.getElementById('close-batch-btn');
const addTextBtn = document.getElementById('add-text-btn');
const importTextsBtn = document.getElementById('import-texts-btn');
const clearAllTextsBtn = document.getElementById('clear-all-texts-btn');
const batchTextsList = document.getElementById('batch-texts-list');
const emptyBatchMessage = document.getElementById('empty-batch-message');
const batchActionSelect = document.getElementById('batch-action-select');
const batchToneSelect = document.getElementById('batch-tone-select');
const batchLanguageSelect = document.getElementById('batch-language-select');
const batchConcurrentSelect = document.getElementById('batch-concurrent-select');
const batchCustomPromptSection = document.getElementById('batch-custom-prompt-section');
const batchCustomPromptSelect = document.getElementById('batch-custom-prompt-select');
const batchCustomPromptInput = document.getElementById('batch-custom-prompt-input');
const batchTotalCount = document.getElementById('batch-total-count');
const batchProcessedCount = document.getElementById('batch-processed-count');
const batchFailedCount = document.getElementById('batch-failed-count');
const batchRemainingCount = document.getElementById('batch-remaining-count');
const batchProgressBar = document.getElementById('batch-progress-bar');
const batchProgressText = document.getElementById('batch-progress-text');
const batchCurrentStatus = document.getElementById('batch-current-status');
const exportResultsBtn = document.getElementById('export-results-btn');
const cancelBatchBtn = document.getElementById('cancel-batch-btn');
const startBatchBtn = document.getElementById('start-batch-btn');

// Text Comparison Elements
const compareBtn = document.getElementById('compare-btn');
const comparisonModal = document.getElementById('comparison-modal');
const closeComparisonBtn = document.getElementById('close-comparison-btn');
const closeComparisonFooterBtn = document.getElementById('close-comparison-footer-btn');
const showWordDiff = document.getElementById('show-word-diff');
const showCharDiff = document.getElementById('show-char-diff');
const ignoreWhitespace = document.getElementById('ignore-whitespace');
const ignoreCase = document.getElementById('ignore-case');
const originalWordCount = document.getElementById('original-word-count');
const refinedWordCount = document.getElementById('refined-word-count');
const wordsChanged = document.getElementById('words-changed');
const changePercentage = document.getElementById('change-percentage');
const originalTextComparison = document.getElementById('original-text-comparison');
const refinedTextComparison = document.getElementById('refined-text-comparison');
const unifiedDiff = document.getElementById('unified-diff');
const copyOriginalBtn = document.getElementById('copy-original-btn');
const copyRefinedBtn = document.getElementById('copy-refined-btn');
const exportComparisonBtn = document.getElementById('export-comparison-btn');

// Model Recommendation Elements
const recommendModelBtn = document.getElementById('recommend-model-btn');
const modelRecommendation = document.getElementById('model-recommendation');
const closeRecommendationBtn = document.getElementById('close-recommendation');
const recommendedModelName = document.getElementById('recommended-model-name');
const recommendedModelReason = document.getElementById('recommended-model-reason');
const textAnalysis = document.getElementById('text-analysis');
const modelBenefits = document.getElementById('model-benefits');
const applyRecommendationBtn = document.getElementById('apply-recommendation-btn');
const dismissRecommendationBtn = document.getElementById('dismiss-recommendation-btn');

// Template System Elements
const templatesBtn = document.getElementById('templates-btn');
const templatesModal = document.getElementById('templates-modal');
const closeTemplatesBtn = document.getElementById('close-templates-btn');
const closeTemplatesFooterBtn = document.getElementById('close-templates-footer-btn');
const templateSearchInput = document.getElementById('template-search-input');
const templatesGrid = document.getElementById('templates-grid');
const templatePreviewSection = document.getElementById('template-preview-section');
const previewTitle = document.getElementById('preview-title');
const previewDescription = document.getElementById('preview-description');
const previewCategory = document.getElementById('preview-category');
const previewVariables = document.getElementById('preview-variables');
const previewContent = document.getElementById('preview-content');
const templateVariables = document.getElementById('template-variables');
const variablesGrid = document.getElementById('variables-grid');
const useTemplateBtn = document.getElementById('use-template-btn');
const customizeTemplateBtn = document.getElementById('customize-template-btn');
const createTemplateBtn = document.getElementById('create-template-btn');

// Create Template Modal Elements
const createTemplateModal = document.getElementById('create-template-modal');
const closeCreateTemplateBtn = document.getElementById('close-create-template-btn');
const newTemplateName = document.getElementById('new-template-name');
const newTemplateCategory = document.getElementById('new-template-category');
const newTemplateDescription = document.getElementById('new-template-description');
const newTemplateTags = document.getElementById('new-template-tags');
const newTemplateContent = document.getElementById('new-template-content');
const detectedVariablesSection = document.getElementById('detected-variables-section');
const detectedVariablesList = document.getElementById('detected-variables-list');
const templateCreatorPreviewContent = document.getElementById('template-creator-preview-content');
const clearTemplateFormBtn = document.getElementById('clear-template-form-btn');
const cancelCreateTemplateBtn = document.getElementById('cancel-create-template-btn');
const saveNewTemplateBtn = document.getElementById('save-new-template-btn');

// Enhanced UX Elements
const undoBtn = document.getElementById('undo-btn');
const redoBtn = document.getElementById('redo-btn');
const importTextBtn = document.getElementById('import-text-btn');
const statsBtn = document.getElementById('stats-btn');
const exportBtn = document.getElementById('export-btn');

// Statistics Modal Elements
const statsModal = document.getElementById('stats-modal');
const closeStatsBtn = document.getElementById('close-stats-btn');
const closeStatsFooterBtn = document.getElementById('close-stats-footer-btn');
const refreshStatsBtn = document.getElementById('refresh-stats-btn');
const statCharacters = document.getElementById('stat-characters');
const statWords = document.getElementById('stat-words');
const statSentences = document.getElementById('stat-sentences');
const statParagraphs = document.getElementById('stat-paragraphs');
const statReadingTime = document.getElementById('stat-reading-time');
const statSpeakingTime = document.getElementById('stat-speaking-time');
const statAvgWordLength = document.getElementById('stat-avg-word-length');
const statAvgSentenceLength = document.getElementById('stat-avg-sentence-length');
const complexityReadingLevel = document.getElementById('complexity-reading-level');
const complexityVocabulary = document.getElementById('complexity-vocabulary');
const complexityStructure = document.getElementById('complexity-structure');
const complexityReadingBar = document.getElementById('complexity-reading-bar');
const complexityVocabularyBar = document.getElementById('complexity-vocabulary-bar');
const complexityStructureBar = document.getElementById('complexity-structure-bar');
const analysisCommonWords = document.getElementById('analysis-common-words');
const analysisTextType = document.getElementById('analysis-text-type');
const analysisTone = document.getElementById('analysis-tone');

// Export Modal Elements
const exportModal = document.getElementById('export-modal');
const closeExportBtn = document.getElementById('close-export-btn');
const cancelExportBtn = document.getElementById('cancel-export-btn');
const startExportBtn = document.getElementById('start-export-btn');
const includeOriginal = document.getElementById('include-original');
const includeRefined = document.getElementById('include-refined');
const includeMetadata = document.getElementById('include-metadata');
const includeStatistics = document.getElementById('include-statistics');
const exportFilename = document.getElementById('export-filename');

// File Import Elements
const fileInput = document.getElementById('file-input');
const dragDropArea = document.getElementById('drag-drop-area');

// Productivity Features Elements
const documentsBtn = document.getElementById('documents-btn');
const saveDocumentBtn = document.getElementById('save-document-btn');

// Document Management Modal Elements
const documentsModal = document.getElementById('documents-modal');
const closeDocumentsBtn = document.getElementById('close-documents-btn');
const closeDocumentsFooterBtn = document.getElementById('close-documents-footer-btn');
const workspaceSelect = document.getElementById('workspace-select');
const manageWorkspacesBtn = document.getElementById('manage-workspaces-btn');
const documentSearch = document.getElementById('document-search');
const tagFilter = document.getElementById('tag-filter');
const dateFilter = document.getElementById('date-filter');
const clearFiltersBtn = document.getElementById('clear-filters-btn');
const newDocumentBtn = document.getElementById('new-document-btn');
const importDocumentBtn = document.getElementById('import-document-btn');
const bulkExportBtn = document.getElementById('bulk-export-btn');
const favoritesBtn = document.getElementById('favorites-btn');
const documentsList = document.getElementById('documents-list');
const documentCount = document.getElementById('document-count');
const emptyDocumentsMessage = document.getElementById('empty-documents-message');
const deleteSelectedBtn = document.getElementById('delete-selected-btn');

// Save Document Modal Elements
const saveDocumentModal = document.getElementById('save-document-modal');
const closeSaveDocumentBtn = document.getElementById('close-save-document-btn');
const documentTitle = document.getElementById('document-title');
const documentDescription = document.getElementById('document-description');
const documentWorkspace = document.getElementById('document-workspace');
const documentTags = document.getElementById('document-tags');
const saveAsFavorite = document.getElementById('save-as-favorite');
const saveOriginalText = document.getElementById('save-original-text');
const saveMetadata = document.getElementById('save-metadata');
const cancelSaveDocumentBtn = document.getElementById('cancel-save-document-btn');
const confirmSaveDocumentBtn = document.getElementById('confirm-save-document-btn');

// --- 2. Configuration and Model Data ---
const API_PROVIDERS = {
    ollama: {
        name: 'Ollama (Local)',
        baseUrl: 'http://localhost:11434',
        modelsEndpoint: '/api/tags',
        models: [] // Will be populated dynamically
    },
    openrouter: {
        name: 'OpenRouter (Cloud)',
        baseUrl: 'https://openrouter.ai/api/v1',
        modelsEndpoint: '/models',
        models: [] // Will be populated dynamically
    }
};

// Cache for models to avoid repeated API calls
const modelCache = {
    ollama: { models: [], lastFetch: 0, ttl: 300000 }, // 5 minutes TTL
    openrouter: { models: [], lastFetch: 0, ttl: 3600000 } // 1 hour TTL
};

// Default fallback models in case API calls fail
const FALLBACK_MODELS = {
    ollama: [
        { id: 'gemma:2b-instruct', name: 'Gemma 2B Instruct' },
        { id: 'llama3.2:latest', name: 'Llama 3.2 Latest' },
        { id: 'mistral:latest', name: 'Mistral Latest' }
    ],
    openrouter: [
        { id: 'google/gemma-2-9b-it', name: 'Gemma 2 9B IT' },
        { id: 'meta-llama/llama-3.1-8b-instruct', name: 'Llama 3.1 8B Instruct' },
        { id: 'anthropic/claude-3-haiku', name: 'Claude 3 Haiku' },
        { id: 'openai/gpt-4o-mini', name: 'GPT-4o Mini' }
    ]
};

// Custom Prompts Storage Key
const CUSTOM_PROMPTS_KEY = 'smart_scribe_custom_prompts';

// Default custom prompts
const DEFAULT_CUSTOM_PROMPTS = [
    {
        id: 'email-professional',
        name: 'Professional Email',
        category: 'business',
        description: 'Format text as a professional email',
        prompt: 'Please rewrite the following text as a professional email with proper greeting, body, and closing. Use a {tone} tone and write in {language}:\n\n{text}',
        createdAt: Date.now()
    },
    {
        id: 'social-media-post',
        name: 'Social Media Post',
        category: 'social',
        description: 'Convert text to engaging social media post',
        prompt: 'Transform the following text into an engaging social media post. Make it {tone} and suitable for {language} audience. Add relevant hashtags:\n\n{text}',
        createdAt: Date.now()
    },
    {
        id: 'academic-summary',
        name: 'Academic Summary',
        category: 'academic',
        description: 'Create academic-style summary',
        prompt: 'Create a concise academic summary of the following text. Use formal language, maintain objectivity, and write in {language}:\n\n{text}',
        createdAt: Date.now()
    }
];

// Current editing prompt (for modal)
let currentEditingPrompt = null;

// Batch Processing Variables
let batchTexts = [];
let batchResults = [];
let batchProcessing = false;
let batchQueue = [];
let batchStats = {
    total: 0,
    processed: 0,
    failed: 0,
    remaining: 0
};

// Text Comparison Variables
let originalText = '';
let refinedText = '';
let comparisonData = null;

// Model Recommendation Variables
let currentRecommendation = null;

// Context-aware Processing Variables
const CONTEXT_STORAGE_KEY = 'smart_scribe_context_memory';
const MAX_CONTEXT_ENTRIES = 100;
let contextMemory = [];
let userPatterns = {
    preferredActions: {},
    preferredTones: {},
    preferredLanguages: {},
    preferredModels: {},
    textTypes: {},
    refinementHistory: []
};

// Template System Variables
const TEMPLATES_STORAGE_KEY = 'smart_scribe_templates';
let currentSelectedTemplate = null;
let templateVariableValues = {};

// Enhanced UX Variables
const HISTORY_STORAGE_KEY = 'smart_scribe_text_history';
const MAX_HISTORY_ENTRIES = 50;
let textHistory = [];
let historyIndex = -1;
let isUndoRedoOperation = false;
let selectedExportFormat = 'txt';
let currentTextStats = null;

// Productivity Features Variables
const DOCUMENTS_STORAGE_KEY = 'smart_scribe_documents';
const WORKSPACES_STORAGE_KEY = 'smart_scribe_workspaces';
const FAVORITES_STORAGE_KEY = 'smart_scribe_favorites';
const MAX_DOCUMENTS = 1000;
let documents = [];
let workspaces = ['default'];
let favorites = {
    documents: [],
    prompts: [],
    settings: {}
};
let selectedDocuments = [];
let currentFilters = {
    workspace: 'all',
    search: '',
    tags: '',
    date: ''
};

// Built-in templates
const DEFAULT_TEMPLATES = [
    {
        id: 'email-professional',
        name: 'Professional Email',
        category: 'business',
        description: 'A formal business email template with proper structure',
        variables: ['recipient_name', 'sender_name', 'subject', 'main_message'],
        content: `Subject: {subject}

Dear {recipient_name},

I hope this email finds you well. {main_message}

Please let me know if you have any questions or need further clarification.

Best regards,
{sender_name}`,
        tags: ['email', 'business', 'formal']
    },
    {
        id: 'email-follow-up',
        name: 'Follow-up Email',
        category: 'business',
        description: 'Professional follow-up email template',
        variables: ['recipient_name', 'sender_name', 'previous_topic', 'action_needed'],
        content: `Dear {recipient_name},

I wanted to follow up on our previous discussion regarding {previous_topic}.

{action_needed}

I look forward to hearing from you soon.

Best regards,
{sender_name}`,
        tags: ['email', 'follow-up', 'business']
    },
    {
        id: 'social-linkedin',
        name: 'LinkedIn Post',
        category: 'social',
        description: 'Engaging LinkedIn post template for professional networking',
        variables: ['topic', 'key_insight', 'call_to_action'],
        content: `🚀 Excited to share some thoughts on {topic}!

{key_insight}

What's your experience with this? {call_to_action}

#LinkedIn #Professional #Networking`,
        tags: ['linkedin', 'social', 'professional']
    },
    {
        id: 'social-twitter',
        name: 'Twitter Thread',
        category: 'social',
        description: 'Twitter thread starter template',
        variables: ['topic', 'hook', 'main_points'],
        content: `🧵 Thread: {topic}

{hook}

Here's what I've learned:

{main_points}

What do you think? Let me know in the replies! 👇

#Twitter #Thread`,
        tags: ['twitter', 'thread', 'social']
    },
    {
        id: 'essay-academic',
        name: 'Academic Essay',
        category: 'academic',
        description: 'Structured academic essay template',
        variables: ['topic', 'thesis_statement', 'main_arguments', 'conclusion'],
        content: `# {topic}

## Introduction

{thesis_statement}

## Main Arguments

{main_arguments}

## Conclusion

{conclusion}

In summary, this essay has demonstrated that the thesis statement is supported by the evidence presented.`,
        tags: ['essay', 'academic', 'research']
    },
    {
        id: 'report-business',
        name: 'Business Report',
        category: 'business',
        description: 'Professional business report template',
        variables: ['report_title', 'executive_summary', 'findings', 'recommendations'],
        content: `# {report_title}

## Executive Summary

{executive_summary}

## Key Findings

{findings}

## Recommendations

{recommendations}

## Conclusion

Based on the analysis presented, the following actions are recommended for implementation.`,
        tags: ['report', 'business', 'analysis']
    },
    {
        id: 'story-creative',
        name: 'Short Story',
        category: 'creative',
        description: 'Creative short story template',
        variables: ['protagonist', 'setting', 'conflict', 'resolution'],
        content: `# The Story of {protagonist}

In {setting}, {protagonist} faced an unexpected challenge.

{conflict}

As the story unfolded, {protagonist} discovered that...

{resolution}

And so, the story came to its conclusion, leaving {protagonist} forever changed.`,
        tags: ['story', 'creative', 'fiction']
    },
    {
        id: 'letter-personal',
        name: 'Personal Letter',
        category: 'personal',
        description: 'Warm personal letter template',
        variables: ['recipient_name', 'sender_name', 'occasion', 'personal_message'],
        content: `Dear {recipient_name},

I hope you're doing well! I wanted to reach out because {occasion}.

{personal_message}

I'd love to hear from you soon and catch up properly.

With love,
{sender_name}`,
        tags: ['letter', 'personal', 'friendly']
    },
    {
        id: 'proposal-project',
        name: 'Project Proposal',
        category: 'business',
        description: 'Professional project proposal template',
        variables: ['project_name', 'objective', 'scope', 'timeline', 'budget'],
        content: `# Project Proposal: {project_name}

## Objective
{objective}

## Project Scope
{scope}

## Timeline
{timeline}

## Budget Estimate
{budget}

## Next Steps
Upon approval, we will proceed with the detailed planning phase and begin implementation according to the proposed timeline.`,
        tags: ['proposal', 'project', 'business']
    },
    {
        id: 'blog-post',
        name: 'Blog Post',
        category: 'creative',
        description: 'Engaging blog post template',
        variables: ['title', 'hook', 'main_content', 'conclusion'],
        content: `# {title}

{hook}

## The Main Story

{main_content}

## Wrapping Up

{conclusion}

What are your thoughts on this topic? I'd love to hear from you in the comments below!`,
        tags: ['blog', 'content', 'writing']
    }
];

// Model characteristics database
const MODEL_CHARACTERISTICS = {
    // Ollama models
    'llama3.2:latest': {
        name: 'Llama 3.2',
        provider: 'ollama',
        strengths: ['general-purpose', 'creative-writing', 'conversational'],
        weaknesses: ['technical-writing'],
        optimalLength: { min: 50, max: 2000 },
        languages: ['english', 'spanish', 'french'],
        speed: 'fast',
        quality: 'high'
    },
    'llama3.2:1b': {
        name: 'Llama 3.2 1B',
        provider: 'ollama',
        strengths: ['speed', 'simple-tasks', 'grammar-fixes'],
        weaknesses: ['complex-reasoning', 'long-texts'],
        optimalLength: { min: 10, max: 500 },
        languages: ['english'],
        speed: 'very-fast',
        quality: 'medium'
    },
    'llama3.2:3b': {
        name: 'Llama 3.2 3B',
        provider: 'ollama',
        strengths: ['balanced', 'general-purpose', 'efficiency'],
        weaknesses: ['very-complex-tasks'],
        optimalLength: { min: 20, max: 1500 },
        languages: ['english', 'spanish', 'french'],
        speed: 'fast',
        quality: 'good'
    },
    'mistral:latest': {
        name: 'Mistral',
        provider: 'ollama',
        strengths: ['technical-writing', 'code', 'analysis'],
        weaknesses: ['creative-writing'],
        optimalLength: { min: 100, max: 3000 },
        languages: ['english', 'french'],
        speed: 'medium',
        quality: 'high'
    },
    'gemma2:latest': {
        name: 'Gemma 2',
        provider: 'ollama',
        strengths: ['academic-writing', 'research', 'formal-tone'],
        weaknesses: ['casual-conversation'],
        optimalLength: { min: 200, max: 4000 },
        languages: ['english'],
        speed: 'medium',
        quality: 'very-high'
    },
    // OpenRouter models
    'meta-llama/llama-3.2-3b-instruct:free': {
        name: 'Llama 3.2 3B Instruct',
        provider: 'openrouter',
        strengths: ['general-purpose', 'instructions', 'balanced'],
        weaknesses: ['very-long-texts'],
        optimalLength: { min: 50, max: 2000 },
        languages: ['english', 'spanish', 'french', 'german'],
        speed: 'fast',
        quality: 'good'
    },
    'microsoft/phi-3-mini-128k-instruct:free': {
        name: 'Phi-3 Mini',
        provider: 'openrouter',
        strengths: ['efficiency', 'speed', 'short-texts'],
        weaknesses: ['complex-reasoning', 'long-texts'],
        optimalLength: { min: 10, max: 800 },
        languages: ['english'],
        speed: 'very-fast',
        quality: 'medium'
    },
    'mistralai/mistral-7b-instruct:free': {
        name: 'Mistral 7B Instruct',
        provider: 'openrouter',
        strengths: ['technical-writing', 'instructions', 'structured-output'],
        weaknesses: ['creative-writing'],
        optimalLength: { min: 100, max: 3000 },
        languages: ['english', 'french'],
        speed: 'medium',
        quality: 'high'
    }
};

// --- 3. Helper Functions ---
function updateCharCount() {
    const count = inputText.value.length;
    charCount.textContent = count.toLocaleString();

    // Update character count color based on length
    if (count > 5000) {
        charCount.style.color = 'var(--error-color)';
    } else if (count > 3000) {
        charCount.style.color = 'var(--warning-color)';
    } else {
        charCount.style.color = 'var(--gray-500)';
    }
}

// --- 4. Model Fetching Functions ---
async function fetchOllamaModels() {
    try {
        const response = await fetch(`${API_PROVIDERS.ollama.baseUrl}${API_PROVIDERS.ollama.modelsEndpoint}`);
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();
        return data.models.map(model => ({
            id: model.name,
            name: formatModelName(model.name),
            size: model.size,
            modified: model.modified_at
        })).sort((a, b) => a.name.localeCompare(b.name));
    } catch (error) {
        console.warn('Failed to fetch Ollama models:', error);
        return FALLBACK_MODELS.ollama;
    }
}

async function fetchOpenRouterModels() {
    try {
        const response = await fetch(`${API_PROVIDERS.openrouter.baseUrl}${API_PROVIDERS.openrouter.modelsEndpoint}`);
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();
        // Filter for text models and popular ones
        return data.data
            .filter(model =>
                model.architecture?.output_modalities?.includes('text') &&
                !model.id.includes('vision') &&
                !model.id.includes('embedding') &&
                parseFloat(model.pricing?.prompt || '0') > 0 // Has pricing info
            )
            .map(model => ({
                id: model.id,
                name: model.name || formatModelName(model.id),
                context: model.context_length,
                pricing: model.pricing
            }))
            .sort((a, b) => a.name.localeCompare(b.name))
            .slice(0, 200); // Limit to top 50 models
    } catch (error) {
        console.warn('Failed to fetch OpenRouter models:', error);
        return FALLBACK_MODELS.openrouter;
    }
}

function formatModelName(modelId) {
    // Convert model IDs to readable names
    return modelId
        .replace(/[_-]/g, ' ')
        .replace(/\b\w/g, l => l.toUpperCase())
        .replace(/(\d+)([a-zA-Z])/g, '$1 $2')
        .replace(/\s+/g, ' ')
        .trim();
}

// --- Dynamic UI Update Functions ---
function getCurrentModelDisplayName() {
    const selectedOption = modelSelect.options[modelSelect.selectedIndex];
    if (selectedOption && selectedOption.value) {
        return selectedOption.textContent;
    }
    return 'AI';
}

function updateAISuggestionTitle() {
    const modelName = getCurrentModelDisplayName();
    const shortName = getShortModelName(modelName);
    aiSuggestionTitle.textContent = `${shortName} Output`;
}

function updateLoadingText() {
    const modelName = getCurrentModelDisplayName();
    const shortName = getShortModelName(modelName);
    loadingText.textContent = `${shortName} is thinking...`;
}

function updateOutputPlaceholder() {
    const modelName = getCurrentModelDisplayName();
    const shortName = getShortModelName(modelName);
    outputTextarea.placeholder = `${shortName} refined text will appear here...`;
}

function getShortModelName(fullName) {
    // Extract a short, recognizable name from the full model name
    const name = fullName.toLowerCase();

    // Common model name patterns
    if (name.includes('gemma')) return 'Gemma';
    if (name.includes('llama')) return 'Llama';
    if (name.includes('mistral')) return 'Mistral';
    if (name.includes('claude')) return 'Claude';
    if (name.includes('gpt')) return 'GPT';
    if (name.includes('qwen')) return 'Qwen';
    if (name.includes('phi')) return 'Phi';
    if (name.includes('codellama')) return 'CodeLlama';
    if (name.includes('deepseek')) return 'DeepSeek';
    if (name.includes('solar')) return 'Solar';
    if (name.includes('openchat')) return 'OpenChat';
    if (name.includes('zephyr')) return 'Zephyr';
    if (name.includes('vicuna')) return 'Vicuna';
    if (name.includes('alpaca')) return 'Alpaca';

    // If no specific pattern matches, try to extract the first meaningful word
    const words = fullName.split(' ');
    for (const word of words) {
        if (word.length > 2 && !word.match(/^\d+$/)) {
            return word;
        }
    }

    // Fallback to 'AI'
    return 'AI';
}

function updateAllDynamicElements() {
    updateAISuggestionTitle();
    updateLoadingText();
    updateOutputPlaceholder();
    updateDefaultModelIndicator();
}

// --- Default Model Functions ---
function setDefaultModel() {
    const selectedModel = modelSelect.value;
    const selectedProvider = providerSelect.value;

    if (!selectedModel) {
        alert('Please select a model first.');
        return;
    }

    localStorage.setItem('default_ai_provider', selectedProvider);
    localStorage.setItem('default_ai_model', selectedModel);

    updateDefaultModelIndicator();

    // Show feedback
    const originalText = setDefaultModelBtn.querySelector('.button-text').textContent;
    setDefaultModelBtn.querySelector('.button-text').textContent = 'Set!';
    setDefaultModelBtn.disabled = true;

    setTimeout(() => {
        setDefaultModelBtn.querySelector('.button-text').textContent = originalText;
        setDefaultModelBtn.disabled = false;
    }, 1500);
}

function updateDefaultModelIndicator() {
    const defaultProvider = localStorage.getItem('default_ai_provider');
    const defaultModel = localStorage.getItem('default_ai_model');
    const currentProvider = providerSelect.value;
    const currentModel = modelSelect.value;

    const isDefault = (defaultProvider === currentProvider && defaultModel === currentModel);

    if (isDefault && currentModel) {
        defaultModelIndicator.style.display = 'flex';
        setDefaultModelBtn.style.display = 'none';
    } else {
        defaultModelIndicator.style.display = 'none';
        setDefaultModelBtn.style.display = currentModel ? 'flex' : 'none';
    }
}

function loadDefaultModel() {
    const defaultProvider = localStorage.getItem('default_ai_provider');
    const defaultModel = localStorage.getItem('default_ai_model');

    if (defaultProvider && defaultModel) {
        providerSelect.value = defaultProvider;
        // Update models for the provider, then set the default model
        updateModelOptions().then(() => {
            if (modelSelect.querySelector(`option[value="${defaultModel}"]`)) {
                modelSelect.value = defaultModel;
                updateAllDynamicElements();
            }
        });
    }
}

async function getModelsForProvider(provider) {
    const cache = modelCache[provider];
    const now = Date.now();

    // Check if we have cached models that are still valid
    if (cache.models.length > 0 && (now - cache.lastFetch) < cache.ttl) {
        return cache.models;
    }

    // Fetch fresh models
    let models;
    if (provider === 'ollama') {
        models = await fetchOllamaModels();
    } else if (provider === 'openrouter') {
        models = await fetchOpenRouterModels();
    } else {
        models = FALLBACK_MODELS[provider] || [];
    }

    // Update cache
    cache.models = models;
    cache.lastFetch = now;

    return models;
}

async function updateModelOptions() {
    const selectedProvider = providerSelect.value;

    // Show loading state
    modelSelect.innerHTML = '<option value="">Loading models...</option>';
    modelSelect.disabled = true;

    try {
        const models = await getModelsForProvider(selectedProvider);

        // Clear existing options
        modelSelect.innerHTML = '';

        if (models.length === 0) {
            modelSelect.innerHTML = '<option value="">No models available</option>';
        } else {
            // Add new options
            models.forEach(model => {
                const option = document.createElement('option');
                option.value = model.id;
                option.textContent = model.name;
                if (model.context) {
                    option.title = `Context: ${model.context.toLocaleString()} tokens`;
                }
                modelSelect.appendChild(option);
            });
        }

        modelSelect.disabled = false;

        // Update dynamic UI elements after models are loaded
        updateAllDynamicElements();

    } catch (error) {
        console.error('Error updating model options:', error);
        modelSelect.innerHTML = '<option value="">Error loading models</option>';
        modelSelect.disabled = false;
    }

    // Show/hide OpenRouter settings section
    if (selectedProvider === 'openrouter') {
        openrouterSettings.style.display = 'block';
    } else {
        openrouterSettings.style.display = 'none';
    }
}

function toggleApiKeyVisibility() {
    const isPassword = apiKeyInput.type === 'password';
    apiKeyInput.type = isPassword ? 'text' : 'password';
    toggleKeyVisibility.textContent = isPassword ? '🙈' : '👁️';
}

function saveApiKey() {
    const apiKey = apiKeyInput.value.trim();
    if (apiKey) {
        localStorage.setItem('openrouter_api_key', apiKey);
    }
}

function loadApiKey() {
    const savedKey = localStorage.getItem('openrouter_api_key');
    if (savedKey) {
        apiKeyInput.value = savedKey;
    }
}

// --- Settings Modal Functions ---
function openSettingsModal() {
    settingsModal.classList.add('active');
    document.body.style.overflow = 'hidden';
}

function closeSettingsModal() {
    settingsModal.classList.remove('active');
    document.body.style.overflow = '';
}

function loadSettings() {
    // Load provider and model settings
    const savedProvider = localStorage.getItem('ai_provider') || 'ollama';
    const savedModel = localStorage.getItem('ai_model');
    const savedLanguage = localStorage.getItem('language') || 'English';
    const savedTone = localStorage.getItem('tone') || 'professional';
    const savedAction = localStorage.getItem('action') || 'improve-style';

    // Load default preferences
    const defaultAction = localStorage.getItem('default_action') || 'improve-style';
    const defaultTone = localStorage.getItem('default_tone') || 'professional';
    const defaultLanguage = localStorage.getItem('default_language') || 'English';

    // Load app settings
    const autoSave = localStorage.getItem('auto_save_settings') !== 'false';
    const showChar = localStorage.getItem('show_char_count') !== 'false';
    const rememberText = localStorage.getItem('remember_last_text') === 'true';

    // Apply current settings
    providerSelect.value = savedProvider;
    langSelect.value = savedLanguage;
    toneSelect.value = savedTone;
    actionSelect.value = savedAction;

    // Apply default preferences
    defaultActionSelect.value = defaultAction;
    defaultToneSelect.value = defaultTone;
    defaultLanguageSelect.value = defaultLanguage;

    // Apply app settings
    autoSaveSettings.checked = autoSave;
    showCharCount.checked = showChar;
    rememberLastText.checked = rememberText;

    if (savedModel) {
        modelSelect.value = savedModel;
    }

    // Apply character count visibility
    const charCountElement = document.querySelector('.char-counter');
    if (charCountElement) {
        charCountElement.style.display = showChar ? 'block' : 'none';
    }

    // Load saved text if enabled
    if (rememberText) {
        const savedText = localStorage.getItem('last_input_text');
        if (savedText && inputText) {
            inputText.value = savedText;
            updateCharCount();
        }
    }
}

function saveSettings() {
    if (autoSaveSettings.checked) {
        localStorage.setItem('ai_provider', providerSelect.value);
        localStorage.setItem('ai_model', modelSelect.value);
        localStorage.setItem('language', langSelect.value);
        localStorage.setItem('tone', toneSelect.value);
        localStorage.setItem('action', actionSelect.value);

        // Save default preferences
        localStorage.setItem('default_action', defaultActionSelect.value);
        localStorage.setItem('default_tone', defaultToneSelect.value);
        localStorage.setItem('default_language', defaultLanguageSelect.value);

        // Save app settings
        localStorage.setItem('auto_save_settings', autoSaveSettings.checked);
        localStorage.setItem('show_char_count', showCharCount.checked);
        localStorage.setItem('remember_last_text', rememberLastText.checked);
    }

    // Apply character count visibility
    const charCountElement = document.querySelector('.char-counter');
    if (charCountElement) {
        charCountElement.style.display = showCharCount.checked ? 'block' : 'none';
    }

    // Save current text if enabled
    if (rememberLastText.checked && inputText) {
        localStorage.setItem('last_input_text', inputText.value);
    } else {
        localStorage.removeItem('last_input_text');
    }
}

function resetSettings() {
    if (confirm('Are you sure you want to reset all settings to defaults?')) {
        // Clear localStorage
        localStorage.removeItem('ai_provider');
        localStorage.removeItem('ai_model');
        localStorage.removeItem('language');
        localStorage.removeItem('tone');
        localStorage.removeItem('action');
        localStorage.removeItem('auto_save_settings');
        localStorage.removeItem('show_char_count');
        localStorage.removeItem('openrouter_api_key');

        // Reset to defaults
        providerSelect.value = 'ollama';
        langSelect.value = 'English';
        toneSelect.value = 'professional';
        actionSelect.value = 'improve-style';
        autoSaveSettings.checked = true;
        showCharCount.checked = true;
        apiKeyInput.value = '';

        // Refresh models
        updateModelOptions();

        // Apply settings
        saveSettings();

        alert('Settings have been reset to defaults.');
    }
}

function setLoadingState(isLoading) {
    if (isLoading) {
        refineBtn.classList.add('loading');
        refineBtn.disabled = true;
        copyBtn.disabled = true;
        loadingOverlay.classList.add('active');
        statusDot.style.backgroundColor = 'var(--warning-color)';
        statusText.textContent = 'Processing...';
    } else {
        refineBtn.classList.remove('loading');
        refineBtn.disabled = false;
        loadingOverlay.classList.remove('active');
        statusDot.style.backgroundColor = 'var(--success-color)';
        statusText.textContent = 'Ready';

        // Only enable copy if there's actual output
        if (outputText.value && !outputText.value.includes('Error:') && !outputText.value.includes('Please enter')) {
            copyBtn.disabled = false;
        }
    }
}

function setErrorState(message) {
    statusDot.style.backgroundColor = 'var(--error-color)';
    statusText.textContent = 'Error';
    outputText.value = message;
    setLoadingState(false);
}

// --- 4. API Functions ---
async function callOllamaAPI(model, prompt) {
    // First check if Ollama is running
    try {
        await fetch(`${API_PROVIDERS.ollama.baseUrl}/api/tags`, { method: 'GET' });
    } catch (error) {
        throw new Error('Ollama server is not running. Please start Ollama and try again.');
    }

    const response = await fetch(`${API_PROVIDERS.ollama.baseUrl}/api/generate`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            model: model,
            prompt: prompt,
            stream: false,
            options: {
                temperature: 0.7,
                top_p: 0.9,
                top_k: 40,
                num_predict: 4000
            }
        })
    });

    if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        let errorMessage = `Ollama API error: ${response.status} ${response.statusText}`;

        if (errorData.error) {
            errorMessage += ` - ${errorData.error}`;
        }

        // Add specific error handling
        if (response.status === 404) {
            errorMessage += `\nModel "${model}" not found. Please pull the model first: ollama pull ${model}`;
        } else if (response.status === 500) {
            errorMessage += '\nOllama server error. Please check the server logs.';
        }

        throw new Error(errorMessage);
    }

    const data = await response.json();

    if (!data.response) {
        throw new Error('Invalid response format from Ollama API');
    }

    return data.response.trim();
}

async function callOpenRouterAPI(model, prompt, apiKey) {
    const response = await fetch(`${API_PROVIDERS.openrouter.baseUrl}/chat/completions`, {
        method: 'POST',
        headers: {
            'Authorization': `Bearer ${apiKey}`,
            'Content-Type': 'application/json',
            'HTTP-Referer': window.location.origin || 'https://smart-scribe.app',
            'X-Title': 'Smart Scribe - AI Writing Assistant'
        },
        body: JSON.stringify({
            model: model,
            messages: [
                {
                    role: 'system',
                    content: 'You are Smart Scribe, an expert writing assistant. Follow the user\'s instructions precisely and return only the refined text without explanations.'
                },
                {
                    role: 'user',
                    content: prompt
                }
            ],
            temperature: 0.7,
            max_tokens: 4000,
            stream: false,
            // Add optional parameters for better results
            top_p: 0.9,
            frequency_penalty: 0.1,
            presence_penalty: 0.1
        })
    });

    if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        let errorMessage = `OpenRouter API error: ${response.status} ${response.statusText}`;

        if (errorData.error) {
            if (errorData.error.message) {
                errorMessage += ` - ${errorData.error.message}`;
            }
            if (errorData.error.code) {
                errorMessage += ` (Code: ${errorData.error.code})`;
            }
        }

        // Add specific error handling for common issues
        if (response.status === 401) {
            errorMessage += '\nPlease check your API key.';
        } else if (response.status === 402) {
            errorMessage += '\nInsufficient credits. Please add credits to your OpenRouter account.';
        } else if (response.status === 429) {
            errorMessage += '\nRate limit exceeded. Please try again in a moment.';
        }

        throw new Error(errorMessage);
    }

    const data = await response.json();

    if (!data.choices || !data.choices[0] || !data.choices[0].message) {
        throw new Error('Invalid response format from OpenRouter API');
    }

    return data.choices[0].message.content.trim();
}

async function refineText(userText, language, tone, action) {
    const provider = providerSelect.value;
    const model = modelSelect.value;
    const prompt = buildPrompt(userText, language, tone, action);

    if (provider === 'ollama') {
        return await callOllamaAPI(model, prompt);
    } else if (provider === 'openrouter') {
        const apiKey = apiKeyInput.value.trim();
        if (!apiKey) {
            throw new Error('OpenRouter API key is required. Please enter your API key.');
        }
        saveApiKey(); // Save the API key for future use
        return await callOpenRouterAPI(model, prompt, apiKey);
    } else {
        throw new Error('Invalid API provider selected.');
    }
}

// --- 5. Event Listeners ---

// Settings Modal
settingsBtn.addEventListener('click', openSettingsModal);
closeSettingsBtn.addEventListener('click', closeSettingsModal);

// Close modal when clicking outside
settingsModal.addEventListener('click', (e) => {
    if (e.target === settingsModal) {
        closeSettingsModal();
    }
});

// Close modal with Escape key
document.addEventListener('keydown', (e) => {
    if (e.key === 'Escape' && settingsModal.classList.contains('active')) {
        closeSettingsModal();
    }
});

// Provider selection change
providerSelect.addEventListener('change', () => {
    updateModelOptions();
    if (autoSaveSettings.checked) {
        saveSettings();
    }
});

// Refresh models button
refreshModelsBtn.addEventListener('click', async () => {
    const provider = providerSelect.value;

    // Clear cache for the current provider
    modelCache[provider].models = [];
    modelCache[provider].lastFetch = 0;

    // Show loading state
    refreshModelsBtn.disabled = true;
    refreshModelsBtn.classList.add('loading');

    try {
        await updateModelOptions();
    } finally {
        refreshModelsBtn.disabled = false;
        refreshModelsBtn.classList.remove('loading');
    }
});

// API key visibility toggle
toggleKeyVisibility.addEventListener('click', toggleApiKeyVisibility);

// API key input (save on change)
apiKeyInput.addEventListener('change', saveApiKey);

// Settings form elements
langSelect.addEventListener('change', () => {
    if (autoSaveSettings.checked) saveSettings();
});

toneSelect.addEventListener('change', () => {
    if (autoSaveSettings.checked) saveSettings();
});

actionSelect.addEventListener('change', () => {
    if (autoSaveSettings.checked) saveSettings();
});

modelSelect.addEventListener('change', () => {
    updateAllDynamicElements();
    if (autoSaveSettings.checked) saveSettings();
});

// Set default model button
setDefaultModelBtn.addEventListener('click', setDefaultModel);

// Custom Prompts Event Listeners
actionSelect.addEventListener('change', handleActionChange);
savedPromptsSelect.addEventListener('change', loadSelectedPrompt);
newPromptBtn.addEventListener('click', () => {
    clearPromptEditor();
    openPromptsModal();
});
managePromptsBtn.addEventListener('click', openPromptsModal);

// Prompts Modal Event Listeners
closePromptsBtn.addEventListener('click', closePromptsModal);
promptsModal.addEventListener('click', (e) => {
    if (e.target === promptsModal) {
        closePromptsModal();
    }
});
savePromptBtn.addEventListener('click', savePrompt);
cancelPromptBtn.addEventListener('click', clearPromptEditor);
deletePromptBtn.addEventListener('click', () => {
    if (currentEditingPrompt) {
        deletePrompt(currentEditingPrompt.id);
    }
});

// Batch Processing Event Listeners
batchProcessBtn.addEventListener('click', openBatchModal);
closeBatchBtn.addEventListener('click', closeBatchModal);
batchModal.addEventListener('click', (e) => {
    if (e.target === batchModal) {
        closeBatchModal();
    }
});

addTextBtn.addEventListener('click', addTextToBatch);
importTextsBtn.addEventListener('click', importTextsFromFile);
clearAllTextsBtn.addEventListener('click', clearAllBatchTexts);

batchActionSelect.addEventListener('change', updateBatchCustomPromptSection);
batchCustomPromptSelect.addEventListener('change', loadBatchCustomPrompt);

startBatchBtn.addEventListener('click', () => {
    if (batchProcessing) {
        stopBatchProcessing();
    } else {
        startBatchProcessing();
    }
});

cancelBatchBtn.addEventListener('click', closeBatchModal);
exportResultsBtn.addEventListener('click', exportBatchResults);

// Text Comparison Event Listeners
compareBtn.addEventListener('click', openComparisonModal);
closeComparisonBtn.addEventListener('click', closeComparisonModal);
closeComparisonFooterBtn.addEventListener('click', closeComparisonModal);
comparisonModal.addEventListener('click', (e) => {
    if (e.target === comparisonModal) {
        closeComparisonModal();
    }
});

// Comparison options change listeners
showWordDiff.addEventListener('change', performComparison);
showCharDiff.addEventListener('change', performComparison);
ignoreWhitespace.addEventListener('change', performComparison);
ignoreCase.addEventListener('change', performComparison);

copyOriginalBtn.addEventListener('click', () => copyComparisonText(originalText));
copyRefinedBtn.addEventListener('click', () => copyComparisonText(refinedText));
exportComparisonBtn.addEventListener('click', exportComparison);

// Model Recommendation Event Listeners
recommendModelBtn.addEventListener('click', analyzeTextForRecommendation);
closeRecommendationBtn.addEventListener('click', dismissRecommendation);
applyRecommendationBtn.addEventListener('click', applyRecommendation);
dismissRecommendationBtn.addEventListener('click', dismissRecommendation);

// Template System Event Listeners
templatesBtn.addEventListener('click', openTemplatesModal);
closeTemplatesBtn.addEventListener('click', closeTemplatesModal);
closeTemplatesFooterBtn.addEventListener('click', closeTemplatesModal);
templatesModal.addEventListener('click', (e) => {
    if (e.target === templatesModal) {
        closeTemplatesModal();
    }
});

// Template category buttons
document.addEventListener('click', (e) => {
    if (e.target.classList.contains('template-category-btn') || e.target.closest('.template-category-btn')) {
        const btn = e.target.classList.contains('template-category-btn') ? e.target : e.target.closest('.template-category-btn');
        const category = btn.dataset.category;
        filterTemplatesByCategory(category);
    }
});

// Template search
templateSearchInput.addEventListener('input', searchTemplates);

// Template actions
useTemplateBtn.addEventListener('click', useTemplate);
customizeTemplateBtn.addEventListener('click', showCustomizeVariables);

// Create Template Modal Event Listeners
createTemplateBtn.addEventListener('click', openCreateTemplateModal);
closeCreateTemplateBtn.addEventListener('click', closeCreateTemplateModal);
cancelCreateTemplateBtn.addEventListener('click', closeCreateTemplateModal);
createTemplateModal.addEventListener('click', (e) => {
    if (e.target === createTemplateModal) {
        closeCreateTemplateModal();
    }
});

// Template form event listeners
newTemplateContent.addEventListener('input', updateTemplatePreview);
clearTemplateFormBtn.addEventListener('click', clearTemplateForm);
saveNewTemplateBtn.addEventListener('click', saveNewTemplate);

// Enhanced UX Event Listeners
undoBtn.addEventListener('click', undo);
redoBtn.addEventListener('click', redo);
importTextBtn.addEventListener('click', importTextFromFile);
statsBtn.addEventListener('click', openStatsModal);
exportBtn.addEventListener('click', openExportModal);

// File Import Event Listeners
fileInput.addEventListener('change', (e) => {
    const file = e.target.files[0];
    if (file) {
        handleFileImport(file);
    }
    // Reset file input
    fileInput.value = '';
});

// Statistics Modal Event Listeners
closeStatsBtn.addEventListener('click', closeStatsModal);
closeStatsFooterBtn.addEventListener('click', closeStatsModal);
refreshStatsBtn.addEventListener('click', refreshStatistics);
statsModal.addEventListener('click', (e) => {
    if (e.target === statsModal) {
        closeStatsModal();
    }
});

// Export Modal Event Listeners
closeExportBtn.addEventListener('click', closeExportModal);
cancelExportBtn.addEventListener('click', closeExportModal);
startExportBtn.addEventListener('click', exportText);
exportModal.addEventListener('click', (e) => {
    if (e.target === exportModal) {
        closeExportModal();
    }
});

// Export format selection
document.addEventListener('click', (e) => {
    if (e.target.closest('.format-option')) {
        const formatOption = e.target.closest('.format-option');
        const format = formatOption.dataset.format;
        selectExportFormat(format);
    }
});

// Text input history tracking
inputText.addEventListener('input', () => {
    if (!isUndoRedoOperation) {
        // Debounce history saving
        clearTimeout(inputText.historyTimeout);
        inputText.historyTimeout = setTimeout(() => {
            saveToHistory(inputText.value, 'edit');
        }, 1000);
    }
});

// Keyboard shortcuts
document.addEventListener('keydown', (e) => {
    // Ctrl+Z for undo
    if (e.ctrlKey && e.key === 'z' && !e.shiftKey) {
        e.preventDefault();
        undo();
    }
    // Ctrl+Y or Ctrl+Shift+Z for redo
    if ((e.ctrlKey && e.key === 'y') || (e.ctrlKey && e.shiftKey && e.key === 'Z')) {
        e.preventDefault();
        redo();
    }
    // Ctrl+I for import
    if (e.ctrlKey && e.key === 'i') {
        e.preventDefault();
        importTextFromFile();
    }
    // Ctrl+E for export
    if (e.ctrlKey && e.key === 'e') {
        e.preventDefault();
        if (outputText.value.trim()) {
            openExportModal();
        }
    }
});

// Productivity Features Event Listeners
documentsBtn.addEventListener('click', openDocumentsModal);
saveDocumentBtn.addEventListener('click', openSaveDocumentModal);

// Document Management Modal Event Listeners
closeDocumentsBtn.addEventListener('click', closeDocumentsModal);
closeDocumentsFooterBtn.addEventListener('click', closeDocumentsModal);
documentsModal.addEventListener('click', (e) => {
    if (e.target === documentsModal) {
        closeDocumentsModal();
    }
});

// Save Document Modal Event Listeners
closeSaveDocumentBtn.addEventListener('click', closeSaveDocumentModal);
cancelSaveDocumentBtn.addEventListener('click', closeSaveDocumentModal);
confirmSaveDocumentBtn.addEventListener('click', saveDocument);
saveDocumentModal.addEventListener('click', (e) => {
    if (e.target === saveDocumentModal) {
        closeSaveDocumentModal();
    }
});

// Document filtering and search
workspaceSelect.addEventListener('change', (e) => {
    currentFilters.workspace = e.target.value;
    displayDocuments();
});

documentSearch.addEventListener('input', (e) => {
    currentFilters.search = e.target.value;
    // Debounce search
    clearTimeout(documentSearch.searchTimeout);
    documentSearch.searchTimeout = setTimeout(() => {
        displayDocuments();
    }, 300);
});

tagFilter.addEventListener('change', (e) => {
    currentFilters.tags = e.target.value;
    displayDocuments();
});

dateFilter.addEventListener('change', (e) => {
    currentFilters.date = e.target.value;
    displayDocuments();
});

clearFiltersBtn.addEventListener('click', () => {
    currentFilters = { workspace: 'all', search: '', tags: '', date: '' };
    workspaceSelect.value = 'all';
    documentSearch.value = '';
    tagFilter.value = '';
    dateFilter.value = '';
    displayDocuments();
});

// Quick actions
newDocumentBtn.addEventListener('click', () => {
    inputText.value = '';
    outputText.value = '';
    updateCharCount();
    copyBtn.disabled = true;
    compareBtn.style.display = 'none';
    exportBtn.style.display = 'none';
    saveDocumentBtn.style.display = 'none';
    closeDocumentsModal();
});

importDocumentBtn.addEventListener('click', () => {
    closeDocumentsModal();
    importTextFromFile();
});

deleteSelectedBtn.addEventListener('click', deleteSelectedDocuments);

// Settings buttons
resetSettingsBtn.addEventListener('click', resetSettings);
saveSettingsBtn.addEventListener('click', () => {
    saveSettings();
    closeSettingsModal();

    // Show feedback
    const originalText = saveSettingsBtn.querySelector('.button-icon').textContent;
    saveSettingsBtn.querySelector('.button-icon').textContent = '✅';
    setTimeout(() => {
        saveSettingsBtn.querySelector('.button-icon').textContent = originalText;
    }, 1000);
});

// Auto-save toggle
autoSaveSettings.addEventListener('change', () => {
    localStorage.setItem('auto_save_settings', autoSaveSettings.checked);
});

// Character count toggle
showCharCount.addEventListener('change', () => {
    const charCountElement = document.querySelector('.char-counter');
    if (charCountElement) {
        charCountElement.style.display = showCharCount.checked ? 'block' : 'none';
    }
    if (autoSaveSettings.checked) saveSettings();
});

// Default preference event listeners
defaultActionSelect.addEventListener('change', () => {
    if (autoSaveSettings.checked) saveSettings();
});

defaultToneSelect.addEventListener('change', () => {
    if (autoSaveSettings.checked) saveSettings();
});

defaultLanguageSelect.addEventListener('change', () => {
    if (autoSaveSettings.checked) saveSettings();
});

// Remember last text toggle
rememberLastText.addEventListener('change', () => {
    if (autoSaveSettings.checked) saveSettings();
});

// Save text on input change if remember is enabled and context-aware suggestions
inputText.addEventListener('input', () => {
    updateCharCount();
    if (rememberLastText.checked) {
        localStorage.setItem('last_input_text', inputText.value);
    }

    // Debounced context-aware suggestions
    clearTimeout(inputText.suggestionTimeout);
    inputText.suggestionTimeout = setTimeout(() => {
        const text = inputText.value.trim();
        if (text.length > 50) { // Only suggest for substantial text
            const suggestions = getContextualSuggestions(text);
            if (suggestions && suggestions.confidence > 60) {
                applySuggestionsToUI(suggestions);
            }
        }
    }, 2000); // Wait 2 seconds after user stops typing
});

// Character count update (removed - now handled in the input listener above)

// Clear button
clearBtn.addEventListener('click', () => {
    inputText.value = '';
    outputText.value = '';
    updateCharCount();
    copyBtn.disabled = true;

    // Reset copy button state
    copyIcon.textContent = '📋';
    copyText.textContent = 'Copy';
    copyBtn.classList.remove('copied');
});

// --- 6. Event Listener for "Refine Text" button ---
refineBtn.addEventListener('click', async () => {
    const userText = inputText.value.trim();

    // Get selected values from dropdowns
    const language = langSelect.value;
    const tone = toneSelect.value;
    const action = actionSelect.value;
    const provider = providerSelect.value;

    // Basic validation: Prevent running on empty text
    if (!userText) {
        setErrorState("Please enter some text to refine.");
        return;
    }

    // Check text length
    if (userText.length > 10000) {
        setErrorState("Text is too long. Please limit to 10,000 characters.");
        return;
    }

    // Validate model selection
    if (!modelSelect.value) {
        setErrorState("Please select a model first.");
        return;
    }

    // Validate API key for OpenRouter
    if (provider === 'openrouter' && !apiKeyInput.value.trim()) {
        setErrorState("Please enter your OpenRouter API key to use cloud models.");
        apiKeyInput.focus();
        return;
    }

    // Show loading state
    setLoadingState(true);

    try {
        const refinedText = await refineText(userText, language, tone, action);
        outputText.value = refinedText;
        setLoadingState(false);
        showCompareButton();
        exportBtn.style.display = 'inline-flex';
        saveDocumentBtn.style.display = 'inline-flex';

        // Add to context memory for learning
        const textAnalysis = analyzeTextCharacteristics(userText);
        addToContextMemory(userText, refinedText, {
            action,
            tone,
            language,
            model: modelSelect.value,
            provider: providerSelect.value
        }, textAnalysis);

    } catch (error) {
        console.error("Error communicating with API:", error);

        let errorMessage = `Error: ${error.message}`;

        if (provider === 'ollama') {
            errorMessage += `\n\nMake sure Ollama is running and the selected model is downloaded.\nTo install: ollama pull ${modelSelect.value}`;
        } else if (provider === 'openrouter') {
            errorMessage += `\n\nPlease check your API key and internet connection.\nGet your API key from: https://openrouter.ai/keys`;
        }

        setErrorState(errorMessage);
    }
});

// --- 6. Event Listener for "Copy to Clipboard" button ---
copyBtn.addEventListener('click', async () => {
    const textToCopy = outputText.value;
    if (textToCopy && !textToCopy.includes('Error:') && !textToCopy.includes('Please enter')) {
        try {
            await navigator.clipboard.writeText(textToCopy);

            // Provide visual feedback
            copyIcon.textContent = '✅';
            copyText.textContent = 'Copied!';
            copyBtn.classList.add('copied');

            // Reset after 2 seconds
            setTimeout(() => {
                copyIcon.textContent = '📋';
                copyText.textContent = 'Copy';
                copyBtn.classList.remove('copied');
            }, 2000);

        } catch (err) {
            console.error('Failed to copy text: ', err);

            // Fallback for older browsers or permission issues
            try {
                // Create a temporary textarea element
                const tempTextarea = document.createElement('textarea');
                tempTextarea.value = textToCopy;
                document.body.appendChild(tempTextarea);
                tempTextarea.select();
                document.execCommand('copy');
                document.body.removeChild(tempTextarea);

                // Show success feedback
                copyIcon.textContent = '✅';
                copyText.textContent = 'Copied!';
                copyBtn.classList.add('copied');

                setTimeout(() => {
                    copyIcon.textContent = '📋';
                    copyText.textContent = 'Copy';
                    copyBtn.classList.remove('copied');
                }, 2000);

            } catch (fallbackErr) {
                console.error('Fallback copy failed: ', fallbackErr);
                alert('Could not copy text. Please copy manually.');
            }
        }
    }
});


// --- 7. Keyboard Shortcuts ---
document.addEventListener('keydown', (e) => {
    // Ctrl/Cmd + Enter to refine text
    if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
        e.preventDefault();
        if (!refineBtn.disabled && inputText.value.trim()) {
            refineBtn.click();
        }
    }

    // Ctrl/Cmd + K to clear text
    if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
        e.preventDefault();
        clearBtn.click();
    }

    // Alt + S to open settings
    if (e.altKey && e.key === 's') {
        e.preventDefault();
        if (!settingsModal.classList.contains('active')) {
            openSettingsModal();
        }
    }

    // Ctrl/Cmd + C when output is focused to copy
    if ((e.ctrlKey || e.metaKey) && e.key === 'c' && document.activeElement === outputText) {
        e.preventDefault();
        if (!copyBtn.disabled) {
            copyBtn.click();
        }
    }
});

// --- 8. Initialize UI ---
async function initializeUI() {
    updateCharCount();
    copyBtn.disabled = true;

    // Load saved settings, context memory, and text history
    loadSettings();
    loadApiKey();
    loadContextMemory();
    loadHistoryFromStorage();

    // Initialize first history entry if empty
    if (textHistory.length === 0) {
        saveToHistory('', 'init');
    }

    // Setup drag and drop functionality
    setupDragAndDrop();

    // Load productivity features data
    loadDocuments();
    loadWorkspaces();
    loadFavorites();

    // Add tooltips for keyboard shortcuts
    refineBtn.title = 'Refine Text (Ctrl+Enter)';
    clearBtn.title = 'Clear Text (Ctrl+K)';
    refreshModelsBtn.title = 'Refresh available models';
    settingsBtn.title = 'Open Settings (Alt+S)';

    // Load default model if available, otherwise initialize normally
    const hasDefaultModel = localStorage.getItem('default_ai_provider') && localStorage.getItem('default_ai_model');

    if (hasDefaultModel) {
        loadDefaultModel();
    } else {
        // Initialize model options based on selected provider
        try {
            await updateModelOptions();
        } catch (error) {
            console.error('Failed to initialize models:', error);
            // Show fallback models
            const provider = providerSelect.value;
            const fallbackModels = FALLBACK_MODELS[provider] || [];

            modelSelect.innerHTML = '';
            if (fallbackModels.length > 0) {
                fallbackModels.forEach(model => {
                    const option = document.createElement('option');
                    option.value = model.id;
                    option.textContent = model.name;
                    modelSelect.appendChild(option);
                });
            } else {
                modelSelect.innerHTML = '<option value="">No models available</option>';
            }
        }
    }

    // Update dynamic UI elements
    updateAllDynamicElements();

    // Focus on input text area
    inputText.focus();
}

// --- 9. Custom Prompts Management Functions ---
function loadCustomPrompts() {
    const saved = localStorage.getItem(CUSTOM_PROMPTS_KEY);
    if (saved) {
        try {
            return JSON.parse(saved);
        } catch (error) {
            console.error('Error parsing saved prompts:', error);
        }
    }

    // Return default prompts if none saved
    const defaultPrompts = [...DEFAULT_CUSTOM_PROMPTS];
    saveCustomPrompts(defaultPrompts);
    return defaultPrompts;
}

function saveCustomPrompts(prompts) {
    localStorage.setItem(CUSTOM_PROMPTS_KEY, JSON.stringify(prompts));
}

function generatePromptId() {
    return 'prompt_' + Date.now() + '_' + Math.random().toString(36).substring(2, 11);
}

function updateSavedPromptsSelect() {
    const prompts = loadCustomPrompts();
    savedPromptsSelect.innerHTML = '<option value="">Select a saved prompt...</option>';

    // Group prompts by category
    const categories = {};
    prompts.forEach(prompt => {
        if (!categories[prompt.category]) {
            categories[prompt.category] = [];
        }
        categories[prompt.category].push(prompt);
    });

    // Add options grouped by category
    Object.keys(categories).sort().forEach(category => {
        const optgroup = document.createElement('optgroup');
        optgroup.label = category.charAt(0).toUpperCase() + category.slice(1);

        categories[category].forEach(prompt => {
            const option = document.createElement('option');
            option.value = prompt.id;
            option.textContent = prompt.name;
            option.title = prompt.description || prompt.prompt.substring(0, 100) + '...';
            optgroup.appendChild(option);
        });

        savedPromptsSelect.appendChild(optgroup);
    });
}

function loadSelectedPrompt() {
    const selectedId = savedPromptsSelect.value;
    if (!selectedId) {
        customPromptInput.value = '';
        return;
    }

    const prompts = loadCustomPrompts();
    const prompt = prompts.find(p => p.id === selectedId);
    if (prompt) {
        customPromptInput.value = prompt.prompt;
    }
}

function showCustomPromptsSection() {
    customPromptsSection.style.display = 'block';
    updateSavedPromptsSelect();
}

function hideCustomPromptsSection() {
    customPromptsSection.style.display = 'none';
    customPromptInput.value = '';
    savedPromptsSelect.value = '';
}

function handleActionChange() {
    const selectedAction = actionSelect.value;
    if (selectedAction === 'custom-prompt') {
        showCustomPromptsSection();
    } else {
        hideCustomPromptsSection();
    }
}

// --- 10. Function to build the detailed prompt ---
function buildPrompt(userText, language, tone, action) {
    // Handle custom prompts
    if (action === 'custom-prompt') {
        const customPrompt = customPromptInput.value.trim();
        if (!customPrompt) {
            throw new Error('Please enter a custom prompt or select a saved one.');
        }

        // Replace variables in custom prompt
        return customPrompt
            .replace(/{text}/g, userText)
            .replace(/{language}/g, language)
            .replace(/{tone}/g, tone);
    }

    // Detailed descriptions for each Tone option
    const toneDescriptions = {
        professional: "Formal and professional for a work environment.",
        casual: "Casual and friendly, as if speaking to a friend.",
        confident: "Strong, assertive, and self-assured.",
        friendly: "Warm, approachable, and personable."
    };

    // Detailed descriptions for each Action option
    const actionDescriptions = {
        'improve-style': "Improve the overall style, clarity, and professionalism of the text. Ensure natural flow and appropriate vocabulary.",
        'fix-grammar': "Correct all grammar, spelling, and punctuation errors without changing the original meaning or style significantly.",
        'shorten': "Make the text more concise and to the point, removing redundant words or phrases while retaining key information.",
        'expand': "Elaborate on the original text, adding more detail, examples, or context to make it more comprehensive."
    };

    // Construct the full prompt string
    return `You are Smart Scribe, an expert multilingual writing assistant. Your task is to refine the user's text based on the provided instructions. Follow these rules strictly:

1. Language: Respond in ${language}
2. Tone: Use a ${tone} tone - ${toneDescriptions[tone]}
3. Action: ${actionDescriptions[action]}
4. Only return the refined text, no explanations or additional commentary.
5. Maintain the original meaning and intent while applying the requested changes.

Original text to refine:
${userText}

Refined text:`;
}

// --- 11. Batch Processing Functions ---
function openBatchModal() {
    batchModal.classList.add('active');
    document.body.style.overflow = 'hidden';
    loadBatchSettings();
    updateBatchStats();
    updateBatchCustomPromptSection();
}

function closeBatchModal() {
    batchModal.classList.remove('active');
    document.body.style.overflow = '';
    if (batchProcessing) {
        if (confirm('Batch processing is in progress. Are you sure you want to close?')) {
            stopBatchProcessing();
        } else {
            return;
        }
    }
}

function loadBatchSettings() {
    // Load current settings into batch modal
    batchActionSelect.value = actionSelect.value;
    batchToneSelect.value = toneSelect.value;
    batchLanguageSelect.value = langSelect.value;

    // Update custom prompt options
    updateBatchCustomPromptSelect();
}

function updateBatchCustomPromptSection() {
    const isCustomPrompt = batchActionSelect.value === 'custom-prompt';
    batchCustomPromptSection.style.display = isCustomPrompt ? 'block' : 'none';
}

function updateBatchCustomPromptSelect() {
    const prompts = loadCustomPrompts();
    batchCustomPromptSelect.innerHTML = '<option value="">Select a saved prompt...</option>';

    prompts.forEach(prompt => {
        const option = document.createElement('option');
        option.value = prompt.id;
        option.textContent = prompt.name;
        batchCustomPromptSelect.appendChild(option);
    });
}

function loadBatchCustomPrompt() {
    const selectedId = batchCustomPromptSelect.value;
    if (!selectedId) {
        batchCustomPromptInput.value = '';
        return;
    }

    const prompts = loadCustomPrompts();
    const prompt = prompts.find(p => p.id === selectedId);
    if (prompt) {
        batchCustomPromptInput.value = prompt.prompt;
    }
}

function addTextToBatch() {
    const text = prompt('Enter text to add to batch:');
    if (text && text.trim()) {
        const batchText = {
            id: generateBatchTextId(),
            text: text.trim(),
            status: 'pending',
            result: null,
            error: null,
            addedAt: Date.now()
        };

        batchTexts.push(batchText);
        updateBatchTextsList();
        updateBatchStats();
    }
}

function generateBatchTextId() {
    return 'batch_' + Date.now() + '_' + Math.random().toString(36).substring(2, 11);
}

function importTextsFromFile() {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.txt,.csv';
    input.onchange = (e) => {
        const file = e.target.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = (e) => {
                const content = e.target.result;
                const texts = content.split('\n').filter(line => line.trim());

                texts.forEach(text => {
                    if (text.trim()) {
                        const batchText = {
                            id: generateBatchTextId(),
                            text: text.trim(),
                            status: 'pending',
                            result: null,
                            error: null,
                            addedAt: Date.now()
                        };
                        batchTexts.push(batchText);
                    }
                });

                updateBatchTextsList();
                updateBatchStats();
            };
            reader.readAsText(file);
        }
    };
    input.click();
}

function clearAllBatchTexts() {
    if (batchProcessing) {
        alert('Cannot clear texts while processing is in progress.');
        return;
    }

    if (batchTexts.length > 0 && confirm('Are you sure you want to clear all texts?')) {
        batchTexts = [];
        batchResults = [];
        updateBatchTextsList();
        updateBatchStats();
        exportResultsBtn.style.display = 'none';
    }
}

function updateBatchTextsList() {
    batchTextsList.innerHTML = '';

    if (batchTexts.length === 0) {
        emptyBatchMessage.style.display = 'block';
        return;
    }

    emptyBatchMessage.style.display = 'none';

    batchTexts.forEach((batchText, index) => {
        const item = createBatchTextItem(batchText, index);
        batchTextsList.appendChild(item);
    });
}

function createBatchTextItem(batchText, index) {
    const item = document.createElement('div');
    item.className = `batch-text-item ${batchText.status}`;
    item.dataset.batchTextId = batchText.id;

    const statusIcon = getStatusIcon(batchText.status);
    const statusText = getStatusText(batchText.status);

    item.innerHTML = `
        <div class="batch-text-content">
            <div class="batch-text-preview">${escapeHtml(batchText.text.substring(0, 150))}${batchText.text.length > 150 ? '...' : ''}</div>
            <div class="batch-text-status">
                <span>${statusIcon}</span>
                <span>${statusText}</span>
                ${batchText.error ? `<span style="color: var(--red-600);">- ${batchText.error}</span>` : ''}
            </div>
        </div>
        <div class="batch-text-actions">
            ${batchText.result ? '<button class="batch-text-action-btn view-result-btn" title="View result">👁️</button>' : ''}
            <button class="batch-text-action-btn edit-text-btn" title="Edit text">✏️</button>
            <button class="batch-text-action-btn delete-text-btn" title="Remove text">🗑️</button>
        </div>
    `;

    // Add event listeners
    const viewBtn = item.querySelector('.view-result-btn');
    if (viewBtn) {
        viewBtn.addEventListener('click', () => viewBatchResult(batchText));
    }

    item.querySelector('.edit-text-btn').addEventListener('click', () => editBatchText(index));
    item.querySelector('.delete-text-btn').addEventListener('click', () => deleteBatchText(index));

    return item;
}

function getStatusIcon(status) {
    switch (status) {
        case 'pending': return '⏳';
        case 'processing': return '🔄';
        case 'completed': return '✅';
        case 'failed': return '❌';
        default: return '⏳';
    }
}

function getStatusText(status) {
    switch (status) {
        case 'pending': return 'Pending';
        case 'processing': return 'Processing...';
        case 'completed': return 'Completed';
        case 'failed': return 'Failed';
        default: return 'Unknown';
    }
}

function viewBatchResult(batchText) {
    if (batchText.result) {
        const modal = document.createElement('div');
        modal.className = 'settings-modal active';
        modal.innerHTML = `
            <div class="settings-modal-content">
                <div class="settings-header">
                    <h2 class="settings-title">Batch Result</h2>
                    <button class="close-settings-btn" onclick="this.closest('.settings-modal').remove()">✕</button>
                </div>
                <div class="settings-body">
                    <div class="setting-item">
                        <label class="setting-label">Original Text:</label>
                        <textarea readonly class="setting-textarea" rows="4">${escapeHtml(batchText.text)}</textarea>
                    </div>
                    <div class="setting-item">
                        <label class="setting-label">Processed Result:</label>
                        <textarea readonly class="setting-textarea" rows="6">${escapeHtml(batchText.result)}</textarea>
                    </div>
                </div>
                <div class="settings-footer">
                    <button onclick="navigator.clipboard.writeText('${batchText.result.replace(/'/g, "\\'")}'); this.textContent='Copied!'; setTimeout(() => this.textContent='Copy Result', 1000)" class="save-settings-btn">Copy Result</button>
                </div>
            </div>
        `;
        document.body.appendChild(modal);
    }
}

function editBatchText(index) {
    if (batchProcessing) {
        alert('Cannot edit texts while processing is in progress.');
        return;
    }

    const batchText = batchTexts[index];
    const newText = prompt('Edit text:', batchText.text);
    if (newText !== null && newText.trim()) {
        batchTexts[index].text = newText.trim();
        batchTexts[index].status = 'pending';
        batchTexts[index].result = null;
        batchTexts[index].error = null;
        updateBatchTextsList();
        updateBatchStats();
    }
}

function deleteBatchText(index) {
    if (batchProcessing) {
        alert('Cannot delete texts while processing is in progress.');
        return;
    }

    if (confirm('Are you sure you want to remove this text?')) {
        batchTexts.splice(index, 1);
        updateBatchTextsList();
        updateBatchStats();
    }
}

function updateBatchStats() {
    const total = batchTexts.length;
    const processed = batchTexts.filter(t => t.status === 'completed').length;
    const failed = batchTexts.filter(t => t.status === 'failed').length;
    const remaining = batchTexts.filter(t => t.status === 'pending' || t.status === 'processing').length;

    batchStats = { total, processed, failed, remaining };

    batchTotalCount.textContent = total;
    batchProcessedCount.textContent = processed;
    batchFailedCount.textContent = failed;
    batchRemainingCount.textContent = remaining;

    const progress = total > 0 ? ((processed + failed) / total) * 100 : 0;
    batchProgressBar.style.width = `${progress}%`;
    batchProgressText.textContent = `${Math.round(progress)}%`;

    // Update start button state
    startBatchBtn.disabled = total === 0 || batchProcessing;

    // Show export button if there are completed results
    if (processed > 0) {
        exportResultsBtn.style.display = 'block';
    }
}

async function startBatchProcessing() {
    if (batchTexts.length === 0) {
        alert('Please add some texts to process.');
        return;
    }

    const provider = providerSelect.value;
    const model = modelSelect.value;

    if (!model) {
        alert('Please select an AI model first.');
        return;
    }

    // Validate custom prompt if needed
    if (batchActionSelect.value === 'custom-prompt') {
        const customPrompt = batchCustomPromptInput.value.trim();
        if (!customPrompt) {
            alert('Please enter a custom prompt or select a saved one.');
            return;
        }
    }

    batchProcessing = true;
    startBatchBtn.disabled = true;
    startBatchBtn.innerHTML = '<span class="button-icon">⏸️</span>Stop Processing';

    // Reset all texts to pending
    batchTexts.forEach(text => {
        if (text.status !== 'completed') {
            text.status = 'pending';
            text.error = null;
        }
    });

    const concurrentLimit = parseInt(batchConcurrentSelect.value);
    const pendingTexts = batchTexts.filter(t => t.status === 'pending');

    batchCurrentStatus.textContent = 'Processing texts...';

    try {
        await processBatchTexts(pendingTexts, concurrentLimit);
        batchCurrentStatus.textContent = 'Batch processing completed!';
    } catch (error) {
        batchCurrentStatus.textContent = 'Batch processing stopped.';
    }

    batchProcessing = false;
    startBatchBtn.disabled = false;
    startBatchBtn.innerHTML = '<span class="button-icon">▶️</span>Start Processing';
    updateBatchStats();
}

function stopBatchProcessing() {
    batchProcessing = false;
    batchCurrentStatus.textContent = 'Processing stopped by user.';
}

async function processBatchTexts(texts, concurrentLimit) {
    const chunks = [];
    for (let i = 0; i < texts.length; i += concurrentLimit) {
        chunks.push(texts.slice(i, i + concurrentLimit));
    }

    for (const chunk of chunks) {
        if (!batchProcessing) break;

        const promises = chunk.map(text => processBatchText(text));
        await Promise.allSettled(promises);

        updateBatchTextsList();
        updateBatchStats();

        // Small delay between chunks
        await new Promise(resolve => setTimeout(resolve, 100));
    }
}

async function processBatchText(batchText) {
    if (!batchProcessing) return;

    try {
        batchText.status = 'processing';
        updateBatchTextItem(batchText);

        const language = batchLanguageSelect.value;
        const tone = batchToneSelect.value;
        const action = batchActionSelect.value;

        let prompt;
        if (action === 'custom-prompt') {
            const customPrompt = batchCustomPromptInput.value.trim();
            prompt = customPrompt
                .replace(/{text}/g, batchText.text)
                .replace(/{language}/g, language)
                .replace(/{tone}/g, tone);
        } else {
            prompt = buildPrompt(batchText.text, language, tone, action);
        }

        const result = await refineText(batchText.text, language, tone, action);

        if (batchProcessing) {
            batchText.status = 'completed';
            batchText.result = result;
            batchText.error = null;
        }
    } catch (error) {
        if (batchProcessing) {
            batchText.status = 'failed';
            batchText.error = error.message;
        }
    }

    updateBatchTextItem(batchText);
}

function updateBatchTextItem(batchText) {
    const item = document.querySelector(`[data-batch-text-id="${batchText.id}"]`);
    if (item) {
        item.className = `batch-text-item ${batchText.status}`;
        const statusElement = item.querySelector('.batch-text-status');
        if (statusElement) {
            const statusIcon = getStatusIcon(batchText.status);
            const statusText = getStatusText(batchText.status);
            statusElement.innerHTML = `
                <span>${statusIcon}</span>
                <span>${statusText}</span>
                ${batchText.error ? `<span style="color: var(--red-600);">- ${batchText.error}</span>` : ''}
            `;
        }

        // Add view result button if completed
        if (batchText.status === 'completed' && batchText.result) {
            const actionsDiv = item.querySelector('.batch-text-actions');
            if (actionsDiv && !actionsDiv.querySelector('.view-result-btn')) {
                const viewBtn = document.createElement('button');
                viewBtn.className = 'batch-text-action-btn view-result-btn';
                viewBtn.title = 'View result';
                viewBtn.textContent = '👁️';
                viewBtn.addEventListener('click', () => viewBatchResult(batchText));
                actionsDiv.insertBefore(viewBtn, actionsDiv.firstChild);
            }
        }
    }
}

function exportBatchResults() {
    const completedTexts = batchTexts.filter(t => t.status === 'completed' && t.result);

    if (completedTexts.length === 0) {
        alert('No completed results to export.');
        return;
    }

    const csvContent = 'Original Text,Processed Text,Status\n' +
        completedTexts.map(text =>
            `"${text.text.replace(/"/g, '""')}","${text.result.replace(/"/g, '""')}","${text.status}"`
        ).join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `batch_results_${new Date().toISOString().slice(0, 10)}.csv`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
}

// --- 12. Text Comparison Functions ---
function showCompareButton() {
    const hasOriginal = inputText.value.trim().length > 0;
    const hasRefined = outputText.value.trim().length > 0 && !outputText.value.includes('Error:');

    if (hasOriginal && hasRefined) {
        compareBtn.style.display = 'flex';
    } else {
        compareBtn.style.display = 'none';
    }
}

function openComparisonModal() {
    originalText = inputText.value.trim();
    refinedText = outputText.value.trim();

    if (!originalText || !refinedText) {
        alert('Both original and refined texts are required for comparison.');
        return;
    }

    comparisonModal.classList.add('active');
    document.body.style.overflow = 'hidden';

    performComparison();
}

function closeComparisonModal() {
    comparisonModal.classList.remove('active');
    document.body.style.overflow = '';
}

function performComparison() {
    const options = {
        wordLevel: showWordDiff.checked,
        charLevel: showCharDiff.checked,
        ignoreWhitespace: ignoreWhitespace.checked,
        ignoreCase: ignoreCase.checked
    };

    comparisonData = compareTexts(originalText, refinedText, options);

    updateComparisonStats();
    updateComparisonPanels();
    updateUnifiedDiff();
}

function compareTexts(text1, text2, options = {}) {
    let processedText1 = text1;
    let processedText2 = text2;

    // Apply preprocessing options
    if (options.ignoreCase) {
        processedText1 = processedText1.toLowerCase();
        processedText2 = processedText2.toLowerCase();
    }

    if (options.ignoreWhitespace) {
        processedText1 = processedText1.replace(/\s+/g, ' ').trim();
        processedText2 = processedText2.replace(/\s+/g, ' ').trim();
    }

    // Split into words or characters based on options
    const units1 = options.charLevel ?
        processedText1.split('') :
        processedText1.split(/\s+/).filter(word => word.length > 0);

    const units2 = options.charLevel ?
        processedText2.split('') :
        processedText2.split(/\s+/).filter(word => word.length > 0);

    // Calculate differences using a simple diff algorithm
    const diff = calculateDiff(units1, units2);

    return {
        original: text1,
        refined: text2,
        diff: diff,
        stats: calculateStats(units1, units2, diff),
        options: options
    };
}

function calculateDiff(arr1, arr2) {
    const m = arr1.length;
    const n = arr2.length;
    const dp = Array(m + 1).fill(null).map(() => Array(n + 1).fill(0));

    // Fill the DP table
    for (let i = 0; i <= m; i++) {
        for (let j = 0; j <= n; j++) {
            if (i === 0) {
                dp[i][j] = j;
            } else if (j === 0) {
                dp[i][j] = i;
            } else if (arr1[i - 1] === arr2[j - 1]) {
                dp[i][j] = dp[i - 1][j - 1];
            } else {
                dp[i][j] = 1 + Math.min(dp[i - 1][j], dp[i][j - 1], dp[i - 1][j - 1]);
            }
        }
    }

    // Backtrack to find the actual differences
    const diff = [];
    let i = m, j = n;

    while (i > 0 || j > 0) {
        if (i > 0 && j > 0 && arr1[i - 1] === arr2[j - 1]) {
            diff.unshift({ type: 'equal', value: arr1[i - 1] });
            i--;
            j--;
        } else if (i > 0 && (j === 0 || dp[i - 1][j] <= dp[i][j - 1])) {
            diff.unshift({ type: 'delete', value: arr1[i - 1] });
            i--;
        } else {
            diff.unshift({ type: 'insert', value: arr2[j - 1] });
            j--;
        }
    }

    return diff;
}

function calculateStats(units1, units2, diff) {
    const totalOriginal = units1.length;
    const totalRefined = units2.length;

    let unchanged = 0;
    let added = 0;
    let removed = 0;

    diff.forEach(item => {
        switch (item.type) {
            case 'equal':
                unchanged++;
                break;
            case 'insert':
                added++;
                break;
            case 'delete':
                removed++;
                break;
        }
    });

    const changed = added + removed;
    const changePercent = totalOriginal > 0 ? (changed / totalOriginal) * 100 : 0;

    return {
        originalCount: totalOriginal,
        refinedCount: totalRefined,
        unchanged,
        added,
        removed,
        changed,
        changePercent: Math.round(changePercent * 100) / 100
    };
}

function updateComparisonStats() {
    if (!comparisonData) return;

    const stats = comparisonData.stats;

    originalWordCount.textContent = stats.originalCount;
    refinedWordCount.textContent = stats.refinedCount;
    wordsChanged.textContent = stats.changed;
    changePercentage.textContent = `${stats.changePercent}%`;
}

function updateComparisonPanels() {
    if (!comparisonData) return;

    const diff = comparisonData.diff;
    const options = comparisonData.options;

    // Generate highlighted text for both panels
    const originalHighlighted = generateHighlightedText(diff, 'original', options);
    const refinedHighlighted = generateHighlightedText(diff, 'refined', options);

    originalTextComparison.innerHTML = originalHighlighted;
    refinedTextComparison.innerHTML = refinedHighlighted;
}

function generateHighlightedText(diff, side, options) {
    let html = '';
    const separator = options.charLevel ? '' : ' ';

    diff.forEach(item => {
        const value = escapeHtml(item.value);

        switch (item.type) {
            case 'equal':
                html += value + separator;
                break;
            case 'delete':
                if (side === 'original') {
                    html += `<span class="diff-removed">${value}</span>${separator}`;
                }
                break;
            case 'insert':
                if (side === 'refined') {
                    html += `<span class="diff-added">${value}</span>${separator}`;
                }
                break;
        }
    });

    return html.trim();
}

function updateUnifiedDiff() {
    if (!comparisonData) return;

    const diff = comparisonData.diff;
    const options = comparisonData.options;
    let html = '';

    diff.forEach(item => {
        const value = escapeHtml(item.value);
        const separator = options.charLevel ? '' : ' ';

        switch (item.type) {
            case 'equal':
                html += `<span class="diff-line diff-line-context"> ${value}${separator}</span>`;
                break;
            case 'delete':
                html += `<span class="diff-line diff-line-removed">-${value}${separator}</span>`;
                break;
            case 'insert':
                html += `<span class="diff-line diff-line-added">+${value}${separator}</span>`;
                break;
        }
    });

    unifiedDiff.innerHTML = html;
}

function copyComparisonText(text) {
    navigator.clipboard.writeText(text).then(() => {
        // Show temporary feedback
        const event = new CustomEvent('textCopied');
        document.dispatchEvent(event);
    }).catch(err => {
        console.error('Failed to copy text:', err);
        // Fallback for older browsers
        const tempTextarea = document.createElement('textarea');
        tempTextarea.value = text;
        document.body.appendChild(tempTextarea);
        tempTextarea.select();
        document.execCommand('copy');
        document.body.removeChild(tempTextarea);
    });
}

function exportComparison() {
    if (!comparisonData) {
        alert('No comparison data to export.');
        return;
    }

    const stats = comparisonData.stats;
    const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');

    const reportContent = `# Text Comparison Report
Generated: ${new Date().toLocaleString()}

## Statistics
- Original Words: ${stats.originalCount}
- Refined Words: ${stats.refinedCount}
- Words Changed: ${stats.changed}
- Change Percentage: ${stats.changePercent}%

## Original Text
${comparisonData.original}

## Refined Text
${comparisonData.refined}

## Analysis
- Words Added: ${stats.added}
- Words Removed: ${stats.removed}
- Words Unchanged: ${stats.unchanged}
`;

    const blob = new Blob([reportContent], { type: 'text/markdown' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `text_comparison_${timestamp}.md`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
}

// --- 13. AI Model Recommendation Functions ---
function analyzeTextForRecommendation() {
    const text = inputText.value.trim();
    if (!text) {
        alert('Please enter some text first to get a model recommendation.');
        return;
    }

    const analysis = analyzeTextCharacteristics(text);
    const recommendation = recommendBestModel(analysis);

    if (recommendation) {
        currentRecommendation = recommendation;
        displayRecommendation(recommendation, analysis);
    } else {
        alert('Unable to generate a recommendation. Please ensure you have models available.');
    }
}

function analyzeTextCharacteristics(text) {
    const words = text.split(/\s+/).filter(word => word.length > 0);
    const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0);
    const characters = text.length;

    // Analyze text type
    const hasCode = /```|function|class|import|export|const|let|var|\{|\}/.test(text);
    const hasTechnicalTerms = /API|database|algorithm|framework|implementation|configuration/.test(text.toLowerCase());
    const hasAcademicTerms = /research|study|analysis|hypothesis|methodology|conclusion/.test(text.toLowerCase());
    const hasCreativeElements = /story|character|imagine|creative|artistic|narrative/.test(text.toLowerCase());
    const hasFormalTone = /therefore|furthermore|consequently|moreover|nevertheless/.test(text.toLowerCase());
    const hasCasualTone = /hey|awesome|cool|yeah|gonna|wanna/.test(text.toLowerCase());

    // Determine complexity
    const avgWordsPerSentence = words.length / Math.max(sentences.length, 1);
    const avgCharsPerWord = characters / Math.max(words.length, 1);

    let complexity = 'medium';
    if (avgWordsPerSentence > 20 || avgCharsPerWord > 6 || hasTechnicalTerms || hasAcademicTerms) {
        complexity = 'high';
    } else if (avgWordsPerSentence < 10 && avgCharsPerWord < 4) {
        complexity = 'low';
    }

    // Determine text type
    let textType = 'general';
    if (hasCode || hasTechnicalTerms) {
        textType = 'technical';
    } else if (hasAcademicTerms) {
        textType = 'academic';
    } else if (hasCreativeElements) {
        textType = 'creative';
    }

    // Determine tone preference
    let tonePreference = 'neutral';
    if (hasFormalTone || hasAcademicTerms) {
        tonePreference = 'formal';
    } else if (hasCasualTone) {
        tonePreference = 'casual';
    }

    return {
        wordCount: words.length,
        sentenceCount: sentences.length,
        characterCount: characters,
        avgWordsPerSentence,
        avgCharsPerWord,
        complexity,
        textType,
        tonePreference,
        hasCode,
        hasTechnicalTerms,
        hasAcademicTerms,
        hasCreativeElements,
        language: langSelect.value.toLowerCase(),
        action: actionSelect.value,
        tone: toneSelect.value
    };
}

function recommendBestModel(analysis) {
    const availableModels = Array.from(modelSelect.options)
        .filter(option => option.value && option.value !== '')
        .map(option => option.value);

    if (availableModels.length === 0) {
        return null;
    }

    let bestModel = null;
    let bestScore = -1;

    availableModels.forEach(modelId => {
        const characteristics = MODEL_CHARACTERISTICS[modelId];
        if (!characteristics) {
            return; // Skip unknown models
        }

        let score = 0;
        let reasons = [];

        // Length optimization
        const isOptimalLength = analysis.characterCount >= characteristics.optimalLength.min &&
                               analysis.characterCount <= characteristics.optimalLength.max;
        if (isOptimalLength) {
            score += 30;
            reasons.push('Optimal text length for this model');
        } else if (analysis.characterCount < characteristics.optimalLength.min) {
            score += 10;
            reasons.push('Text is shorter than optimal, but manageable');
        } else {
            score -= 10;
            reasons.push('Text might be longer than optimal for this model');
        }

        // Task-specific strengths
        if (analysis.textType === 'technical' && characteristics.strengths.includes('technical-writing')) {
            score += 25;
            reasons.push('Excellent for technical writing');
        }
        if (analysis.textType === 'academic' && characteristics.strengths.includes('academic-writing')) {
            score += 25;
            reasons.push('Specialized in academic content');
        }
        if (analysis.textType === 'creative' && characteristics.strengths.includes('creative-writing')) {
            score += 25;
            reasons.push('Great for creative content');
        }
        if (analysis.action === 'fix-grammar' && characteristics.strengths.includes('grammar-fixes')) {
            score += 20;
            reasons.push('Optimized for grammar correction');
        }

        // Language support
        if (characteristics.languages.includes(analysis.language)) {
            score += 15;
            reasons.push(`Native support for ${analysis.language}`);
        }

        // Complexity matching
        if (analysis.complexity === 'high' && characteristics.quality === 'very-high') {
            score += 20;
            reasons.push('High-quality model for complex text');
        } else if (analysis.complexity === 'low' && characteristics.speed === 'very-fast') {
            score += 15;
            reasons.push('Fast processing for simple text');
        }

        // Tone matching
        if (analysis.tonePreference === 'formal' && characteristics.strengths.includes('formal-tone')) {
            score += 15;
            reasons.push('Excellent for formal tone');
        }

        // General purpose bonus
        if (analysis.textType === 'general' && characteristics.strengths.includes('general-purpose')) {
            score += 20;
            reasons.push('Well-suited for general text processing');
        }

        // Efficiency considerations
        if (analysis.wordCount < 100 && characteristics.speed === 'very-fast') {
            score += 10;
            reasons.push('Fast processing for short text');
        }

        if (score > bestScore) {
            bestScore = score;
            bestModel = {
                id: modelId,
                characteristics,
                score,
                reasons: reasons.slice(0, 3) // Top 3 reasons
            };
        }
    });

    return bestModel;
}

function displayRecommendation(recommendation, analysis) {
    const model = recommendation.characteristics;

    // Update recommendation display
    recommendedModelName.textContent = model.name;
    recommendedModelReason.textContent = `Score: ${recommendation.score}/100 - ${recommendation.reasons[0]}`;

    // Update text analysis
    textAnalysis.innerHTML = `
        <h4>📊 Text Analysis</h4>
        <div class="analysis-item">
            <span class="analysis-icon">📝</span>
            <span>${analysis.wordCount} words, ${analysis.sentenceCount} sentences</span>
        </div>
        <div class="analysis-item">
            <span class="analysis-icon">🎯</span>
            <span>Type: ${analysis.textType.charAt(0).toUpperCase() + analysis.textType.slice(1)}</span>
        </div>
        <div class="analysis-item">
            <span class="analysis-icon">⚡</span>
            <span>Complexity: ${analysis.complexity.charAt(0).toUpperCase() + analysis.complexity.slice(1)}</span>
        </div>
        <div class="analysis-item">
            <span class="analysis-icon">🌐</span>
            <span>Language: ${analysis.language.charAt(0).toUpperCase() + analysis.language.slice(1)}</span>
        </div>
    `;

    // Update model benefits
    const benefits = recommendation.reasons.map(reason =>
        `<div class="benefit-item">
            <span class="benefit-icon">✅</span>
            <span>${reason}</span>
        </div>`
    ).join('');

    modelBenefits.innerHTML = `
        <h4>🎯 Why This Model?</h4>
        ${benefits}
        <div class="benefit-item">
            <span class="benefit-icon">⚡</span>
            <span>Speed: ${model.speed.charAt(0).toUpperCase() + model.speed.slice(1)}</span>
        </div>
        <div class="benefit-item">
            <span class="benefit-icon">🏆</span>
            <span>Quality: ${model.quality.charAt(0).toUpperCase() + model.quality.slice(1)}</span>
        </div>
    `;

    // Show recommendation
    modelRecommendation.style.display = 'block';
}

function applyRecommendation() {
    if (currentRecommendation) {
        modelSelect.value = currentRecommendation.id;

        // Trigger change event to update UI
        const event = new Event('change', { bubbles: true });
        modelSelect.dispatchEvent(event);

        // Show success feedback
        applyRecommendationBtn.innerHTML = '<span class="button-icon">✅</span>Applied!';
        applyRecommendationBtn.disabled = true;

        setTimeout(() => {
            applyRecommendationBtn.innerHTML = '<span class="button-icon">✅</span>Apply Recommendation';
            applyRecommendationBtn.disabled = false;
            dismissRecommendation();
        }, 1500);
    }
}

function dismissRecommendation() {
    modelRecommendation.style.display = 'none';
    currentRecommendation = null;
}

// --- 14. Context-aware Processing Functions ---
function loadContextMemory() {
    const saved = localStorage.getItem(CONTEXT_STORAGE_KEY);
    if (saved) {
        try {
            const data = JSON.parse(saved);
            contextMemory = data.contextMemory || [];
            userPatterns = { ...userPatterns, ...data.userPatterns };
        } catch (error) {
            console.error('Error loading context memory:', error);
            contextMemory = [];
        }
    }
}

function saveContextMemory() {
    const data = {
        contextMemory,
        userPatterns,
        lastUpdated: Date.now()
    };
    localStorage.setItem(CONTEXT_STORAGE_KEY, JSON.stringify(data));
}

function addToContextMemory(originalText, refinedText, settings, metadata = {}) {
    const contextEntry = {
        id: generateContextId(),
        timestamp: Date.now(),
        originalText: originalText.substring(0, 500), // Limit storage size
        refinedText: refinedText.substring(0, 500),
        settings: {
            action: settings.action,
            tone: settings.tone,
            language: settings.language,
            model: settings.model,
            provider: settings.provider
        },
        metadata: {
            textLength: originalText.length,
            wordCount: originalText.split(/\s+/).length,
            textType: metadata.textType || 'general',
            complexity: metadata.complexity || 'medium',
            ...metadata
        }
    };

    // Add to memory
    contextMemory.unshift(contextEntry);

    // Limit memory size
    if (contextMemory.length > MAX_CONTEXT_ENTRIES) {
        contextMemory = contextMemory.slice(0, MAX_CONTEXT_ENTRIES);
    }

    // Update user patterns
    updateUserPatterns(contextEntry);

    // Save to storage
    saveContextMemory();

    return contextEntry;
}

function generateContextId() {
    return 'ctx_' + Date.now() + '_' + Math.random().toString(36).substring(2, 11);
}

function updateUserPatterns(contextEntry) {
    const settings = contextEntry.settings;
    const metadata = contextEntry.metadata;

    // Track action preferences
    userPatterns.preferredActions[settings.action] =
        (userPatterns.preferredActions[settings.action] || 0) + 1;

    // Track tone preferences
    userPatterns.preferredTones[settings.tone] =
        (userPatterns.preferredTones[settings.tone] || 0) + 1;

    // Track language preferences
    userPatterns.preferredLanguages[settings.language] =
        (userPatterns.preferredLanguages[settings.language] || 0) + 1;

    // Track model preferences
    userPatterns.preferredModels[settings.model] =
        (userPatterns.preferredModels[settings.model] || 0) + 1;

    // Track text type patterns
    const textTypeKey = `${metadata.textType}_${settings.action}`;
    userPatterns.textTypes[textTypeKey] =
        (userPatterns.textTypes[textTypeKey] || 0) + 1;

    // Add to refinement history (keep last 20)
    userPatterns.refinementHistory.unshift({
        timestamp: contextEntry.timestamp,
        action: settings.action,
        tone: settings.tone,
        textType: metadata.textType,
        success: true // Assume success if user didn't report issues
    });

    if (userPatterns.refinementHistory.length > 20) {
        userPatterns.refinementHistory = userPatterns.refinementHistory.slice(0, 20);
    }
}

function getContextualSuggestions(currentText) {
    if (!currentText || contextMemory.length === 0) {
        return null;
    }

    const currentAnalysis = analyzeTextCharacteristics(currentText);
    const suggestions = {
        action: null,
        tone: null,
        model: null,
        confidence: 0,
        reasoning: []
    };

    // Find similar texts in memory
    const similarTexts = findSimilarTexts(currentText, currentAnalysis);

    if (similarTexts.length > 0) {
        // Suggest most common action for similar texts
        const actionCounts = {};
        const toneCounts = {};
        const modelCounts = {};

        similarTexts.forEach(entry => {
            actionCounts[entry.settings.action] = (actionCounts[entry.settings.action] || 0) + 1;
            toneCounts[entry.settings.tone] = (toneCounts[entry.settings.tone] || 0) + 1;
            modelCounts[entry.settings.model] = (modelCounts[entry.settings.model] || 0) + 1;
        });

        // Get most frequent choices
        suggestions.action = Object.keys(actionCounts).reduce((a, b) =>
            actionCounts[a] > actionCounts[b] ? a : b);
        suggestions.tone = Object.keys(toneCounts).reduce((a, b) =>
            toneCounts[a] > toneCounts[b] ? a : b);
        suggestions.model = Object.keys(modelCounts).reduce((a, b) =>
            modelCounts[a] > modelCounts[b] ? a : b);

        suggestions.confidence = Math.min(similarTexts.length * 20, 100);
        suggestions.reasoning.push(`Found ${similarTexts.length} similar texts in your history`);

        if (actionCounts[suggestions.action] > similarTexts.length * 0.6) {
            suggestions.reasoning.push(`You usually use "${suggestions.action}" for this type of text`);
        }
    }

    // Fallback to general user patterns
    if (suggestions.confidence < 50) {
        const mostUsedAction = getMostFrequentChoice(userPatterns.preferredActions);
        const mostUsedTone = getMostFrequentChoice(userPatterns.preferredTones);

        if (mostUsedAction && !suggestions.action) {
            suggestions.action = mostUsedAction;
            suggestions.reasoning.push(`Based on your general preferences`);
        }

        if (mostUsedTone && !suggestions.tone) {
            suggestions.tone = mostUsedTone;
        }

        suggestions.confidence = Math.max(suggestions.confidence, 30);
    }

    return suggestions.confidence > 20 ? suggestions : null;
}

function findSimilarTexts(currentText, currentAnalysis) {
    const similarities = contextMemory.map(entry => {
        let score = 0;

        // Text type similarity
        if (entry.metadata.textType === currentAnalysis.textType) {
            score += 30;
        }

        // Complexity similarity
        if (entry.metadata.complexity === currentAnalysis.complexity) {
            score += 20;
        }

        // Length similarity
        const lengthDiff = Math.abs(entry.metadata.textLength - currentAnalysis.characterCount);
        const lengthSimilarity = Math.max(0, 100 - (lengthDiff / 100));
        score += lengthSimilarity * 0.2;

        // Word count similarity
        const wordDiff = Math.abs(entry.metadata.wordCount - currentAnalysis.wordCount);
        const wordSimilarity = Math.max(0, 100 - (wordDiff / 10));
        score += wordSimilarity * 0.3;

        return { entry, score };
    });

    // Return entries with score > 40, sorted by score
    return similarities
        .filter(item => item.score > 40)
        .sort((a, b) => b.score - a.score)
        .slice(0, 10)
        .map(item => item.entry);
}

function getMostFrequentChoice(choices) {
    if (!choices || Object.keys(choices).length === 0) {
        return null;
    }

    return Object.keys(choices).reduce((a, b) =>
        choices[a] > choices[b] ? a : b);
}

function applySuggestionsToUI(suggestions) {
    if (!suggestions) return;

    let applied = false;

    // Apply action suggestion
    if (suggestions.action && actionSelect.value !== suggestions.action) {
        actionSelect.value = suggestions.action;
        handleActionChange(); // Update UI for custom prompts if needed
        applied = true;
    }

    // Apply tone suggestion
    if (suggestions.tone && toneSelect.value !== suggestions.tone) {
        toneSelect.value = suggestions.tone;
        applied = true;
    }

    // Apply model suggestion (if available)
    if (suggestions.model && modelSelect.value !== suggestions.model) {
        const modelOption = Array.from(modelSelect.options).find(opt => opt.value === suggestions.model);
        if (modelOption) {
            modelSelect.value = suggestions.model;
            applied = true;
        }
    }

    // Show notification if suggestions were applied
    if (applied && suggestions.confidence > 50) {
        showContextualNotification(suggestions);
    }
}

function showContextualNotification(suggestions) {
    // Create a temporary notification
    const notification = document.createElement('div');
    notification.className = 'contextual-notification';
    notification.innerHTML = `
        <div class="notification-content">
            <span class="notification-icon">🧠</span>
            <div class="notification-text">
                <div class="notification-title">Smart Suggestions Applied</div>
                <div class="notification-reason">${suggestions.reasoning[0]}</div>
            </div>
            <button class="notification-close">✕</button>
        </div>
    `;

    // Add to page
    document.body.appendChild(notification);

    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 5000);

    // Manual close
    notification.querySelector('.notification-close').addEventListener('click', () => {
        notification.remove();
    });
}

function getContextualInsights() {
    if (contextMemory.length === 0) {
        return null;
    }

    const insights = {
        totalRefinements: contextMemory.length,
        mostUsedAction: getMostFrequentChoice(userPatterns.preferredActions),
        mostUsedTone: getMostFrequentChoice(userPatterns.preferredTones),
        mostUsedLanguage: getMostFrequentChoice(userPatterns.preferredLanguages),
        mostUsedModel: getMostFrequentChoice(userPatterns.preferredModels),
        averageTextLength: contextMemory.reduce((sum, entry) => sum + entry.metadata.textLength, 0) / contextMemory.length,
        recentActivity: userPatterns.refinementHistory.slice(0, 5)
    };

    return insights;
}

// --- 15. Template System Functions ---
function loadTemplates() {
    const saved = localStorage.getItem(TEMPLATES_STORAGE_KEY);
    if (saved) {
        try {
            const customTemplates = JSON.parse(saved);
            return [...DEFAULT_TEMPLATES, ...customTemplates];
        } catch (error) {
            console.error('Error loading templates:', error);
        }
    }
    return DEFAULT_TEMPLATES;
}

function saveCustomTemplates(customTemplates) {
    localStorage.setItem(TEMPLATES_STORAGE_KEY, JSON.stringify(customTemplates));
}

function openTemplatesModal() {
    templatesModal.classList.add('active');
    document.body.style.overflow = 'hidden';
    loadTemplatesInModal();
    updateTemplateCategoryCounts();
}

function closeTemplatesModal() {
    templatesModal.classList.remove('active');
    document.body.style.overflow = '';
    currentSelectedTemplate = null;
    templatePreviewSection.style.display = 'none';
}

function loadTemplatesInModal() {
    const templates = loadTemplates();
    displayTemplates(templates);
}

function displayTemplates(templates, category = 'all') {
    templatesGrid.innerHTML = '';

    const filteredTemplates = category === 'all' ?
        templates :
        templates.filter(template => template.category === category);

    filteredTemplates.forEach(template => {
        const templateCard = createTemplateCard(template);
        templatesGrid.appendChild(templateCard);
    });

    if (filteredTemplates.length === 0) {
        templatesGrid.innerHTML = `
            <div class="empty-templates-message">
                <span class="empty-icon">📋</span>
                <p>No templates found in this category.</p>
            </div>
        `;
    }
}

function createTemplateCard(template) {
    const card = document.createElement('div');
    card.className = 'template-card';
    card.dataset.templateId = template.id;

    const preview = template.content.substring(0, 100) + (template.content.length > 100 ? '...' : '');

    card.innerHTML = `
        <div class="template-card-header">
            <div class="template-card-title">${escapeHtml(template.name)}</div>
            <div class="template-card-category">${escapeHtml(template.category)}</div>
        </div>
        <div class="template-card-body">
            <div class="template-card-description">${escapeHtml(template.description)}</div>
            <div class="template-card-preview">${escapeHtml(preview)}</div>
        </div>
    `;

    card.addEventListener('click', () => selectTemplate(template));

    return card;
}

function selectTemplate(template) {
    // Remove previous selection
    document.querySelectorAll('.template-card.selected').forEach(card => {
        card.classList.remove('selected');
    });

    // Select current template
    const card = document.querySelector(`[data-template-id="${template.id}"]`);
    if (card) {
        card.classList.add('selected');
    }

    currentSelectedTemplate = template;
    displayTemplatePreview(template);
}

function displayTemplatePreview(template) {
    previewTitle.textContent = template.name;
    previewDescription.textContent = template.description;
    previewCategory.textContent = template.category.charAt(0).toUpperCase() + template.category.slice(1);
    previewVariables.textContent = template.variables.length > 0 ?
        `${template.variables.length} variables` : 'No variables';

    // Show content with variable placeholders highlighted
    const highlightedContent = highlightVariables(template.content, template.variables);
    previewContent.innerHTML = highlightedContent;

    // Setup variable inputs
    setupVariableInputs(template.variables);

    templatePreviewSection.style.display = 'block';
}

function highlightVariables(content, variables) {
    let highlighted = escapeHtml(content);
    variables.forEach(variable => {
        const regex = new RegExp(`\\{${variable}\\}`, 'g');
        highlighted = highlighted.replace(regex, `<span class="template-variable">{${variable}}</span>`);
    });
    return highlighted;
}

function setupVariableInputs(variables) {
    if (variables.length === 0) {
        templateVariables.style.display = 'none';
        return;
    }

    variablesGrid.innerHTML = '';
    templateVariableValues = {};

    variables.forEach(variable => {
        const inputGroup = document.createElement('div');
        inputGroup.className = 'variable-input-group';

        const label = document.createElement('label');
        label.className = 'variable-label';
        label.textContent = variable.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());

        const input = document.createElement('input');
        input.className = 'variable-input';
        input.type = 'text';
        input.placeholder = `Enter ${label.textContent.toLowerCase()}...`;
        input.dataset.variable = variable;

        input.addEventListener('input', (e) => {
            templateVariableValues[variable] = e.target.value;
            updatePreviewWithVariables();
        });

        inputGroup.appendChild(label);
        inputGroup.appendChild(input);
        variablesGrid.appendChild(inputGroup);
    });

    templateVariables.style.display = 'block';
}

function updatePreviewWithVariables() {
    if (!currentSelectedTemplate) return;

    let content = currentSelectedTemplate.content;
    Object.keys(templateVariableValues).forEach(variable => {
        const value = templateVariableValues[variable] || `{${variable}}`;
        const regex = new RegExp(`\\{${variable}\\}`, 'g');
        content = content.replace(regex, value);
    });

    previewContent.textContent = content;
}

function useTemplate() {
    if (!currentSelectedTemplate) return;

    let content = currentSelectedTemplate.content;

    // Replace variables with values
    currentSelectedTemplate.variables.forEach(variable => {
        const value = templateVariableValues[variable] || `{${variable}}`;
        const regex = new RegExp(`\\{${variable}\\}`, 'g');
        content = content.replace(regex, value);
    });

    // Insert into input text area
    inputText.value = content;
    updateCharCount();

    // Close modal
    closeTemplatesModal();

    // Focus on input
    inputText.focus();

    // Show success notification
    showTemplateNotification(`Template "${currentSelectedTemplate.name}" applied successfully!`);
}

function showCustomizeVariables() {
    if (!currentSelectedTemplate || currentSelectedTemplate.variables.length === 0) {
        alert('This template has no customizable variables.');
        return;
    }

    // Scroll to variables section
    templateVariables.scrollIntoView({ behavior: 'smooth' });

    // Focus on first input
    const firstInput = variablesGrid.querySelector('.variable-input');
    if (firstInput) {
        firstInput.focus();
    }
}

function updateTemplateCategoryCounts() {
    const templates = loadTemplates();
    const counts = {
        all: templates.length,
        business: templates.filter(t => t.category === 'business').length,
        academic: templates.filter(t => t.category === 'academic').length,
        social: templates.filter(t => t.category === 'social').length,
        creative: templates.filter(t => t.category === 'creative').length,
        personal: templates.filter(t => t.category === 'personal').length
    };

    Object.keys(counts).forEach(category => {
        const countElement = document.getElementById(`${category}-count`);
        if (countElement) {
            countElement.textContent = counts[category];
        }
    });
}

function filterTemplatesByCategory(category) {
    // Update active category button
    document.querySelectorAll('.template-category-btn').forEach(btn => {
        btn.classList.remove('active');
    });

    const activeBtn = document.querySelector(`[data-category="${category}"]`);
    if (activeBtn) {
        activeBtn.classList.add('active');
    }

    // Filter and display templates
    const templates = loadTemplates();
    displayTemplates(templates, category);

    // Hide preview when changing categories
    templatePreviewSection.style.display = 'none';
    currentSelectedTemplate = null;
}

function searchTemplates() {
    const query = templateSearchInput.value.toLowerCase().trim();
    const templates = loadTemplates();

    if (!query) {
        const activeCategory = document.querySelector('.template-category-btn.active')?.dataset.category || 'all';
        displayTemplates(templates, activeCategory);
        return;
    }

    const filteredTemplates = templates.filter(template =>
        template.name.toLowerCase().includes(query) ||
        template.description.toLowerCase().includes(query) ||
        template.tags.some(tag => tag.toLowerCase().includes(query)) ||
        template.content.toLowerCase().includes(query)
    );

    displayTemplates(filteredTemplates);
}

function showTemplateNotification(message) {
    const notification = document.createElement('div');
    notification.className = 'contextual-notification';
    notification.innerHTML = `
        <div class="notification-content">
            <span class="notification-icon">📋</span>
            <div class="notification-text">
                <div class="notification-title">Template Applied</div>
                <div class="notification-reason">${message}</div>
            </div>
            <button class="notification-close">✕</button>
        </div>
    `;

    document.body.appendChild(notification);

    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 3000);

    notification.querySelector('.notification-close').addEventListener('click', () => {
        notification.remove();
    });
}

function openCreateTemplateModal() {
    createTemplateModal.classList.add('active');
    document.body.style.overflow = 'hidden';
    clearTemplateForm();
}

function closeCreateTemplateModal() {
    createTemplateModal.classList.remove('active');
    document.body.style.overflow = '';
}

function clearTemplateForm() {
    newTemplateName.value = '';
    newTemplateCategory.value = 'business';
    newTemplateDescription.value = '';
    newTemplateTags.value = '';
    newTemplateContent.value = '';
    detectedVariablesSection.style.display = 'none';
    templateCreatorPreviewContent.textContent = 'Enter template content above to see preview...';
}

function detectVariablesInTemplate(content) {
    const variableRegex = /\{([^}]+)\}/g;
    const variables = [];
    let match;

    while ((match = variableRegex.exec(content)) !== null) {
        const variable = match[1].trim();
        if (variable && !variables.includes(variable)) {
            variables.push(variable);
        }
    }

    return variables;
}

function updateTemplatePreview() {
    const content = newTemplateContent.value.trim();

    if (!content) {
        templateCreatorPreviewContent.textContent = 'Enter template content above to see preview...';
        detectedVariablesSection.style.display = 'none';
        return;
    }

    // Update preview with highlighted variables
    const highlightedContent = highlightVariables(content, detectVariablesInTemplate(content));
    templateCreatorPreviewContent.innerHTML = highlightedContent;

    // Update detected variables
    updateDetectedVariables(content);
}

function updateDetectedVariables(content) {
    const variables = detectVariablesInTemplate(content);

    if (variables.length === 0) {
        detectedVariablesSection.style.display = 'none';
        return;
    }

    detectedVariablesList.innerHTML = '';

    variables.forEach(variable => {
        const tag = document.createElement('div');
        tag.className = 'variable-tag';
        tag.innerHTML = `
            <span class="variable-tag-icon">🔧</span>
            <span>{${variable}}</span>
        `;
        detectedVariablesList.appendChild(tag);
    });

    detectedVariablesSection.style.display = 'block';
}

function validateTemplateForm() {
    const name = newTemplateName.value.trim();
    const content = newTemplateContent.value.trim();

    if (!name) {
        alert('Please enter a template name.');
        newTemplateName.focus();
        return false;
    }

    if (!content) {
        alert('Please enter template content.');
        newTemplateContent.focus();
        return false;
    }

    // Check if template name already exists
    const existingTemplates = loadTemplates();
    const nameExists = existingTemplates.some(template =>
        template.name.toLowerCase() === name.toLowerCase()
    );

    if (nameExists) {
        alert('A template with this name already exists. Please choose a different name.');
        newTemplateName.focus();
        return false;
    }

    return true;
}

function saveNewTemplate() {
    if (!validateTemplateForm()) {
        return;
    }

    const name = newTemplateName.value.trim();
    const category = newTemplateCategory.value;
    const description = newTemplateDescription.value.trim();
    const tags = newTemplateTags.value.trim().split(',').map(tag => tag.trim()).filter(tag => tag);
    const content = newTemplateContent.value.trim();
    const variables = detectVariablesInTemplate(content);

    const newTemplate = {
        id: generateTemplateId(),
        name,
        category,
        description: description || `Custom ${category} template`,
        variables,
        content,
        tags: tags.length > 0 ? tags : [category, 'custom'],
        isCustom: true,
        createdAt: Date.now()
    };

    // Load existing custom templates
    const saved = localStorage.getItem(TEMPLATES_STORAGE_KEY);
    let customTemplates = [];
    if (saved) {
        try {
            customTemplates = JSON.parse(saved);
        } catch (error) {
            console.error('Error loading custom templates:', error);
        }
    }

    // Add new template
    customTemplates.push(newTemplate);

    // Save to storage
    saveCustomTemplates(customTemplates);

    // Close modal
    closeCreateTemplateModal();

    // Refresh templates in main modal if it's open
    if (templatesModal.classList.contains('active')) {
        loadTemplatesInModal();
        updateTemplateCategoryCounts();
    }

    // Show success notification
    showTemplateNotification(`Custom template "${name}" created successfully!`);

    // Show success feedback on button
    const originalText = saveNewTemplateBtn.innerHTML;
    saveNewTemplateBtn.innerHTML = '<span class="button-icon">✅</span>Saved!';
    saveNewTemplateBtn.disabled = true;

    setTimeout(() => {
        saveNewTemplateBtn.innerHTML = originalText;
        saveNewTemplateBtn.disabled = false;
    }, 1500);
}

function generateTemplateId() {
    return 'custom_' + Date.now() + '_' + Math.random().toString(36).substring(2, 11);
}

// --- 16. Enhanced User Experience Functions ---

// Undo/Redo Functionality
function saveToHistory(text, operation = 'edit') {
    if (isUndoRedoOperation) return;

    const historyEntry = {
        text: text,
        timestamp: Date.now(),
        operation: operation
    };

    // Remove any entries after current index (when user made new changes after undo)
    if (historyIndex < textHistory.length - 1) {
        textHistory = textHistory.slice(0, historyIndex + 1);
    }

    textHistory.push(historyEntry);

    // Limit history size
    if (textHistory.length > MAX_HISTORY_ENTRIES) {
        textHistory = textHistory.slice(-MAX_HISTORY_ENTRIES);
    }

    historyIndex = textHistory.length - 1;
    updateHistoryButtons();
    saveHistoryToStorage();
}

function loadHistoryFromStorage() {
    const saved = localStorage.getItem(HISTORY_STORAGE_KEY);
    if (saved) {
        try {
            const data = JSON.parse(saved);
            textHistory = data.history || [];
            historyIndex = data.index || -1;
        } catch (error) {
            console.error('Error loading text history:', error);
            textHistory = [];
            historyIndex = -1;
        }
    }
    updateHistoryButtons();
}

function saveHistoryToStorage() {
    const data = {
        history: textHistory,
        index: historyIndex,
        lastUpdated: Date.now()
    };
    localStorage.setItem(HISTORY_STORAGE_KEY, JSON.stringify(data));
}

function undo() {
    if (historyIndex > 0) {
        historyIndex--;
        const entry = textHistory[historyIndex];

        isUndoRedoOperation = true;
        inputText.value = entry.text;
        updateCharCount();
        isUndoRedoOperation = false;

        updateHistoryButtons();
        saveHistoryToStorage();
    }
}

function redo() {
    if (historyIndex < textHistory.length - 1) {
        historyIndex++;
        const entry = textHistory[historyIndex];

        isUndoRedoOperation = true;
        inputText.value = entry.text;
        updateCharCount();
        isUndoRedoOperation = false;

        updateHistoryButtons();
        saveHistoryToStorage();
    }
}

function updateHistoryButtons() {
    undoBtn.disabled = historyIndex <= 0;
    redoBtn.disabled = historyIndex >= textHistory.length - 1;
}

// Text Import Functionality
function importTextFromFile() {
    fileInput.click();
}

function handleFileImport(file) {
    if (!file) return;

    // Check file type
    const allowedTypes = ['.txt', '.md', '.html', '.doc', '.docx'];
    const fileExtension = '.' + file.name.split('.').pop().toLowerCase();

    if (!allowedTypes.includes(fileExtension)) {
        alert(`Unsupported file type: ${fileExtension}\nSupported types: ${allowedTypes.join(', ')}`);
        return;
    }

    // Check file size (limit to 10MB)
    if (file.size > 10 * 1024 * 1024) {
        alert('File is too large. Please select a file smaller than 10MB.');
        return;
    }

    const currentText = inputText.value.trim();
    if (currentText && !confirm('This will replace your current text. Continue?')) {
        return;
    }

    const reader = new FileReader();

    reader.onload = (e) => {
        try {
            let content = e.target.result;

            // Basic HTML cleanup for .html files
            if (fileExtension === '.html') {
                content = cleanHTMLContent(content);
            }

            // Save current text to history before replacing
            if (currentText) {
                saveToHistory(currentText, 'before_import');
            }

            // Set new content
            inputText.value = content;
            updateCharCount();

            // Save imported content to history
            saveToHistory(content, 'import');

            showImportNotification(`Successfully imported ${file.name} (${formatFileSize(file.size)})`);

        } catch (error) {
            console.error('Error reading file:', error);
            alert('Error reading file. Please try again or select a different file.');
        }
    };

    reader.onerror = () => {
        alert('Error reading file. Please try again.');
    };

    // Read file as text
    reader.readAsText(file, 'UTF-8');
}

function cleanHTMLContent(html) {
    // Create a temporary div to parse HTML
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = html;

    // Remove script and style tags
    const scripts = tempDiv.querySelectorAll('script, style');
    scripts.forEach(el => el.remove());

    // Get text content and clean up whitespace
    let text = tempDiv.textContent || tempDiv.innerText || '';

    // Clean up excessive whitespace
    text = text.replace(/\s+/g, ' ').trim();
    text = text.replace(/\n\s*\n/g, '\n\n'); // Preserve paragraph breaks

    return text;
}

function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// Drag and Drop Functionality
function setupDragAndDrop() {
    const textareaContainer = inputText.parentElement;

    // Prevent default drag behaviors
    ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
        textareaContainer.addEventListener(eventName, preventDefaults, false);
        document.body.addEventListener(eventName, preventDefaults, false);
    });

    // Highlight drop area when item is dragged over it
    ['dragenter', 'dragover'].forEach(eventName => {
        textareaContainer.addEventListener(eventName, highlight, false);
    });

    ['dragleave', 'drop'].forEach(eventName => {
        textareaContainer.addEventListener(eventName, unhighlight, false);
    });

    // Handle dropped files
    textareaContainer.addEventListener('drop', handleDrop, false);
}

function preventDefaults(e) {
    e.preventDefault();
    e.stopPropagation();
}

function highlight(e) {
    dragDropArea.style.display = 'flex';
    dragDropArea.classList.add('drag-over');
}

function unhighlight(e) {
    dragDropArea.classList.remove('drag-over');
    setTimeout(() => {
        if (!dragDropArea.classList.contains('drag-over')) {
            dragDropArea.style.display = 'none';
        }
    }, 100);
}

function handleDrop(e) {
    const dt = e.dataTransfer;
    const files = dt.files;

    if (files.length > 0) {
        handleFileImport(files[0]);
    }

    unhighlight(e);
}

function showImportNotification(message) {
    const notification = document.createElement('div');
    notification.className = 'contextual-notification';
    notification.innerHTML = `
        <div class="notification-content">
            <span class="notification-icon">📁</span>
            <div class="notification-text">
                <div class="notification-title">File Imported</div>
                <div class="notification-reason">${message}</div>
            </div>
            <button class="notification-close">✕</button>
        </div>
    `;

    document.body.appendChild(notification);

    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 3000);

    notification.querySelector('.notification-close').addEventListener('click', () => {
        notification.remove();
    });
}

// Text Statistics Functions
function calculateTextStatistics(text) {
    if (!text || text.trim().length === 0) {
        return {
            characters: 0,
            words: 0,
            sentences: 0,
            paragraphs: 0,
            readingTime: 0,
            speakingTime: 0,
            avgWordLength: 0,
            avgSentenceLength: 0,
            complexity: {
                readingLevel: 0,
                vocabulary: 0,
                structure: 0
            },
            analysis: {
                commonWords: [],
                textType: 'unknown',
                tone: 'neutral'
            }
        };
    }

    const characters = text.length;
    const words = text.split(/\s+/).filter(word => word.length > 0);
    const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0);
    const paragraphs = text.split(/\n\s*\n/).filter(p => p.trim().length > 0);

    // Reading and speaking time (average reading: 200 wpm, speaking: 150 wpm)
    const readingTime = Math.ceil(words.length / 200);
    const speakingTime = Math.ceil(words.length / 150);

    // Average lengths
    const avgWordLength = words.length > 0 ?
        words.reduce((sum, word) => sum + word.length, 0) / words.length : 0;
    const avgSentenceLength = sentences.length > 0 ? words.length / sentences.length : 0;

    // Complexity analysis
    const complexity = analyzeComplexity(text, words, sentences);

    // Text analysis
    const analysis = analyzeTextContent(text, words);

    return {
        characters,
        words: words.length,
        sentences: sentences.length,
        paragraphs: paragraphs.length,
        readingTime,
        speakingTime,
        avgWordLength: Math.round(avgWordLength * 10) / 10,
        avgSentenceLength: Math.round(avgSentenceLength * 10) / 10,
        complexity,
        analysis
    };
}

function analyzeComplexity(text, words, sentences) {
    // Simple complexity analysis
    const avgWordsPerSentence = sentences.length > 0 ? words.length / sentences.length : 0;
    const avgCharsPerWord = words.length > 0 ?
        words.reduce((sum, word) => sum + word.length, 0) / words.length : 0;

    // Reading level (simplified Flesch-Kincaid)
    let readingLevel = 0;
    if (sentences.length > 0 && words.length > 0) {
        readingLevel = Math.max(0, Math.min(100,
            206.835 - (1.015 * avgWordsPerSentence) - (84.6 * (avgCharsPerWord / 4.7))
        ));
    }

    // Vocabulary complexity (based on word length and variety)
    const uniqueWords = new Set(words.map(w => w.toLowerCase()));
    const vocabularyComplexity = Math.min(100,
        (avgCharsPerWord * 10) + (uniqueWords.size / words.length * 50)
    );

    // Sentence structure complexity
    const structureComplexity = Math.min(100, avgWordsPerSentence * 5);

    return {
        readingLevel: Math.round(readingLevel),
        vocabulary: Math.round(vocabularyComplexity),
        structure: Math.round(structureComplexity)
    };
}

function analyzeTextContent(text, words) {
    // Most common words (excluding common stop words)
    const stopWords = new Set(['the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should', 'may', 'might', 'can', 'this', 'that', 'these', 'those']);

    const wordFreq = {};
    words.forEach(word => {
        const cleanWord = word.toLowerCase().replace(/[^\w]/g, '');
        if (cleanWord.length > 2 && !stopWords.has(cleanWord)) {
            wordFreq[cleanWord] = (wordFreq[cleanWord] || 0) + 1;
        }
    });

    const commonWords = Object.entries(wordFreq)
        .sort(([,a], [,b]) => b - a)
        .slice(0, 5)
        .map(([word, count]) => `${word} (${count})`);

    // Text type analysis
    const textLower = text.toLowerCase();
    let textType = 'general';

    if (/\b(research|study|analysis|hypothesis|methodology|conclusion|abstract|introduction)\b/.test(textLower)) {
        textType = 'academic';
    } else if (/\b(business|company|market|sales|revenue|profit|strategy|management)\b/.test(textLower)) {
        textType = 'business';
    } else if (/\b(story|character|plot|narrative|fiction|novel|chapter)\b/.test(textLower)) {
        textType = 'creative';
    } else if (/\b(code|function|algorithm|database|software|programming|technical)\b/.test(textLower)) {
        textType = 'technical';
    }

    // Tone analysis
    let tone = 'neutral';
    const positiveWords = /\b(excellent|great|amazing|wonderful|fantastic|good|positive|success|achieve|improve)\b/gi;
    const negativeWords = /\b(bad|terrible|awful|horrible|negative|fail|problem|issue|difficult|challenge)\b/gi;
    const formalWords = /\b(therefore|furthermore|consequently|moreover|nevertheless|however|thus|hence)\b/gi;

    const positiveCount = (text.match(positiveWords) || []).length;
    const negativeCount = (text.match(negativeWords) || []).length;
    const formalCount = (text.match(formalWords) || []).length;

    if (formalCount > 2) {
        tone = 'formal';
    } else if (positiveCount > negativeCount + 1) {
        tone = 'positive';
    } else if (negativeCount > positiveCount + 1) {
        tone = 'negative';
    }

    return {
        commonWords: commonWords.length > 0 ? commonWords.join(', ') : 'No significant words found',
        textType,
        tone
    };
}

function openStatsModal() {
    const text = outputText.value || inputText.value;
    currentTextStats = calculateTextStatistics(text);
    displayTextStatistics(currentTextStats);

    statsModal.classList.add('active');
    document.body.style.overflow = 'hidden';
}

function closeStatsModal() {
    statsModal.classList.remove('active');
    document.body.style.overflow = '';
}

function displayTextStatistics(stats) {
    // Basic statistics
    statCharacters.textContent = stats.characters.toLocaleString();
    statWords.textContent = stats.words.toLocaleString();
    statSentences.textContent = stats.sentences.toLocaleString();
    statParagraphs.textContent = stats.paragraphs.toLocaleString();

    // Reading statistics
    statReadingTime.textContent = `${stats.readingTime} min`;
    statSpeakingTime.textContent = `${stats.speakingTime} min`;
    statAvgWordLength.textContent = stats.avgWordLength;
    statAvgSentenceLength.textContent = stats.avgSentenceLength;

    // Complexity analysis
    complexityReadingLevel.textContent = `${stats.complexity.readingLevel}%`;
    complexityVocabulary.textContent = `${stats.complexity.vocabulary}%`;
    complexityStructure.textContent = `${stats.complexity.structure}%`;

    // Update complexity bars
    complexityReadingBar.style.width = `${stats.complexity.readingLevel}%`;
    complexityVocabularyBar.style.width = `${stats.complexity.vocabulary}%`;
    complexityStructureBar.style.width = `${stats.complexity.structure}%`;

    // Text analysis
    analysisCommonWords.textContent = stats.analysis.commonWords;
    analysisTextType.textContent = stats.analysis.textType.charAt(0).toUpperCase() + stats.analysis.textType.slice(1);
    analysisTone.textContent = stats.analysis.tone.charAt(0).toUpperCase() + stats.analysis.tone.slice(1);
}

function refreshStatistics() {
    const text = outputText.value || inputText.value;
    currentTextStats = calculateTextStatistics(text);
    displayTextStatistics(currentTextStats);

    // Show refresh feedback
    const originalText = refreshStatsBtn.innerHTML;
    refreshStatsBtn.innerHTML = '<span class="button-icon">✅</span>Refreshed!';
    refreshStatsBtn.disabled = true;

    setTimeout(() => {
        refreshStatsBtn.innerHTML = originalText;
        refreshStatsBtn.disabled = false;
    }, 1000);
}

// Export Functions
function openExportModal() {
    exportModal.classList.add('active');
    document.body.style.overflow = 'hidden';

    // Set default filename with timestamp
    const now = new Date();
    const timestamp = now.toISOString().slice(0, 19).replace(/[:.]/g, '-');
    exportFilename.value = `smart-scribe-export-${timestamp}`;

    // Reset format selection
    document.querySelectorAll('.format-option').forEach(option => {
        option.classList.remove('selected');
    });
    document.querySelector('[data-format="txt"]').classList.add('selected');
    selectedExportFormat = 'txt';
}

function closeExportModal() {
    exportModal.classList.remove('active');
    document.body.style.overflow = '';
}

function selectExportFormat(format) {
    document.querySelectorAll('.format-option').forEach(option => {
        option.classList.remove('selected');
    });

    const selectedOption = document.querySelector(`[data-format="${format}"]`);
    if (selectedOption) {
        selectedOption.classList.add('selected');
        selectedExportFormat = format;
    }
}

function exportText() {
    const filename = exportFilename.value.trim() || 'smart-scribe-export';
    const includeOrig = includeOriginal.checked;
    const includeRef = includeRefined.checked;
    const includeMeta = includeMetadata.checked;
    const includeStats = includeStatistics.checked;

    if (!includeOrig && !includeRef) {
        alert('Please select at least one text to include in the export.');
        return;
    }

    let content = '';
    let mimeType = 'text/plain';
    let extension = '.txt';

    // Generate content based on format
    switch (selectedExportFormat) {
        case 'txt':
            content = generatePlainTextExport(includeOrig, includeRef, includeMeta, includeStats);
            mimeType = 'text/plain';
            extension = '.txt';
            break;
        case 'md':
            content = generateMarkdownExport(includeOrig, includeRef, includeMeta, includeStats);
            mimeType = 'text/markdown';
            extension = '.md';
            break;
        case 'html':
            content = generateHTMLExport(includeOrig, includeRef, includeMeta, includeStats);
            mimeType = 'text/html';
            extension = '.html';
            break;
        case 'pdf':
            // For PDF, we'll generate HTML and let the browser handle PDF conversion
            content = generateHTMLExport(includeOrig, includeRef, includeMeta, includeStats);
            mimeType = 'text/html';
            extension = '.html';
            break;
    }

    // Create and download file
    const blob = new Blob([content], { type: mimeType });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename + extension;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    // Close modal and show success
    closeExportModal();
    showExportNotification(`File exported as ${filename}${extension}`);
}

function generatePlainTextExport(includeOrig, includeRef, includeMeta, includeStats) {
    let content = '';
    const timestamp = new Date().toLocaleString();

    content += `Smart Scribe Export\n`;
    content += `Generated: ${timestamp}\n`;
    content += `${'='.repeat(50)}\n\n`;

    if (includeOrig && inputText.value.trim()) {
        content += `ORIGINAL TEXT:\n`;
        content += `${'-'.repeat(20)}\n`;
        content += `${inputText.value.trim()}\n\n`;
    }

    if (includeRef && outputText.value.trim()) {
        content += `REFINED TEXT:\n`;
        content += `${'-'.repeat(20)}\n`;
        content += `${outputText.value.trim()}\n\n`;
    }

    if (includeMeta) {
        content += `METADATA:\n`;
        content += `${'-'.repeat(20)}\n`;
        content += `Action: ${actionSelect.value}\n`;
        content += `Tone: ${toneSelect.value}\n`;
        content += `Language: ${langSelect.value}\n`;
        content += `Model: ${modelSelect.value}\n`;
        content += `Export Date: ${timestamp}\n\n`;
    }

    if (includeStats && currentTextStats) {
        content += `STATISTICS:\n`;
        content += `${'-'.repeat(20)}\n`;
        content += `Characters: ${currentTextStats.characters}\n`;
        content += `Words: ${currentTextStats.words}\n`;
        content += `Sentences: ${currentTextStats.sentences}\n`;
        content += `Paragraphs: ${currentTextStats.paragraphs}\n`;
        content += `Reading Time: ${currentTextStats.readingTime} minutes\n`;
        content += `Speaking Time: ${currentTextStats.speakingTime} minutes\n`;
        content += `Average Word Length: ${currentTextStats.avgWordLength}\n`;
        content += `Average Sentence Length: ${currentTextStats.avgSentenceLength}\n`;
        content += `Reading Level: ${currentTextStats.complexity.readingLevel}%\n`;
        content += `Text Type: ${currentTextStats.analysis.textType}\n`;
        content += `Tone: ${currentTextStats.analysis.tone}\n`;
    }

    return content;
}

function generateMarkdownExport(includeOrig, includeRef, includeMeta, includeStats) {
    let content = '';
    const timestamp = new Date().toLocaleString();

    content += `# Smart Scribe Export\n\n`;
    content += `**Generated:** ${timestamp}\n\n`;
    content += `---\n\n`;

    if (includeOrig && inputText.value.trim()) {
        content += `## Original Text\n\n`;
        content += `${inputText.value.trim()}\n\n`;
    }

    if (includeRef && outputText.value.trim()) {
        content += `## Refined Text\n\n`;
        content += `${outputText.value.trim()}\n\n`;
    }

    if (includeMeta) {
        content += `## Metadata\n\n`;
        content += `- **Action:** ${actionSelect.value}\n`;
        content += `- **Tone:** ${toneSelect.value}\n`;
        content += `- **Language:** ${langSelect.value}\n`;
        content += `- **Model:** ${modelSelect.value}\n`;
        content += `- **Export Date:** ${timestamp}\n\n`;
    }

    if (includeStats && currentTextStats) {
        content += `## Statistics\n\n`;
        content += `### Basic Statistics\n`;
        content += `- **Characters:** ${currentTextStats.characters}\n`;
        content += `- **Words:** ${currentTextStats.words}\n`;
        content += `- **Sentences:** ${currentTextStats.sentences}\n`;
        content += `- **Paragraphs:** ${currentTextStats.paragraphs}\n\n`;
        content += `### Reading Statistics\n`;
        content += `- **Reading Time:** ${currentTextStats.readingTime} minutes\n`;
        content += `- **Speaking Time:** ${currentTextStats.speakingTime} minutes\n`;
        content += `- **Average Word Length:** ${currentTextStats.avgWordLength}\n`;
        content += `- **Average Sentence Length:** ${currentTextStats.avgSentenceLength}\n\n`;
        content += `### Analysis\n`;
        content += `- **Reading Level:** ${currentTextStats.complexity.readingLevel}%\n`;
        content += `- **Text Type:** ${currentTextStats.analysis.textType}\n`;
        content += `- **Tone:** ${currentTextStats.analysis.tone}\n`;
    }

    return content;
}

function generateHTMLExport(includeOrig, includeRef, includeMeta, includeStats) {
    const timestamp = new Date().toLocaleString();

    let content = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Smart Scribe Export</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; max-width: 800px; margin: 0 auto; padding: 20px; }
        h1, h2 { color: #333; border-bottom: 2px solid #eee; padding-bottom: 10px; }
        .metadata { background: #f5f5f5; padding: 15px; border-radius: 5px; margin: 20px 0; }
        .stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; }
        .stat-card { background: white; border: 1px solid #ddd; padding: 15px; border-radius: 5px; }
        .text-content { background: white; border: 1px solid #ddd; padding: 20px; border-radius: 5px; margin: 15px 0; }
        .timestamp { color: #666; font-size: 0.9em; }
    </style>
</head>
<body>
    <h1>Smart Scribe Export</h1>
    <p class="timestamp">Generated: ${timestamp}</p>
`;

    if (includeOrig && inputText.value.trim()) {
        content += `
    <h2>Original Text</h2>
    <div class="text-content">${escapeHtml(inputText.value.trim()).replace(/\n/g, '<br>')}</div>
`;
    }

    if (includeRef && outputText.value.trim()) {
        content += `
    <h2>Refined Text</h2>
    <div class="text-content">${escapeHtml(outputText.value.trim()).replace(/\n/g, '<br>')}</div>
`;
    }

    if (includeMeta) {
        content += `
    <h2>Metadata</h2>
    <div class="metadata">
        <p><strong>Action:</strong> ${escapeHtml(actionSelect.value)}</p>
        <p><strong>Tone:</strong> ${escapeHtml(toneSelect.value)}</p>
        <p><strong>Language:</strong> ${escapeHtml(langSelect.value)}</p>
        <p><strong>Model:</strong> ${escapeHtml(modelSelect.value)}</p>
        <p><strong>Export Date:</strong> ${timestamp}</p>
    </div>
`;
    }

    if (includeStats && currentTextStats) {
        content += `
    <h2>Statistics</h2>
    <div class="stats">
        <div class="stat-card">
            <h3>Basic Statistics</h3>
            <p>Characters: ${currentTextStats.characters}</p>
            <p>Words: ${currentTextStats.words}</p>
            <p>Sentences: ${currentTextStats.sentences}</p>
            <p>Paragraphs: ${currentTextStats.paragraphs}</p>
        </div>
        <div class="stat-card">
            <h3>Reading Statistics</h3>
            <p>Reading Time: ${currentTextStats.readingTime} minutes</p>
            <p>Speaking Time: ${currentTextStats.speakingTime} minutes</p>
            <p>Avg Word Length: ${currentTextStats.avgWordLength}</p>
            <p>Avg Sentence Length: ${currentTextStats.avgSentenceLength}</p>
        </div>
        <div class="stat-card">
            <h3>Analysis</h3>
            <p>Reading Level: ${currentTextStats.complexity.readingLevel}%</p>
            <p>Text Type: ${currentTextStats.analysis.textType}</p>
            <p>Tone: ${currentTextStats.analysis.tone}</p>
        </div>
    </div>
`;
    }

    content += `
</body>
</html>`;

    return content;
}

function showExportNotification(message) {
    const notification = document.createElement('div');
    notification.className = 'contextual-notification';
    notification.innerHTML = `
        <div class="notification-content">
            <span class="notification-icon">💾</span>
            <div class="notification-text">
                <div class="notification-title">Export Complete</div>
                <div class="notification-reason">${message}</div>
            </div>
            <button class="notification-close">✕</button>
        </div>
    `;

    document.body.appendChild(notification);

    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 3000);

    notification.querySelector('.notification-close').addEventListener('click', () => {
        notification.remove();
    });
}

// --- 17. Productivity Features Functions ---

// Document Management Functions
function loadDocuments() {
    const saved = localStorage.getItem(DOCUMENTS_STORAGE_KEY);
    if (saved) {
        try {
            documents = JSON.parse(saved);
        } catch (error) {
            console.error('Error loading documents:', error);
            documents = [];
        }
    }
    return documents;
}

function saveDocuments() {
    localStorage.setItem(DOCUMENTS_STORAGE_KEY, JSON.stringify(documents));
}

function loadWorkspaces() {
    const saved = localStorage.getItem(WORKSPACES_STORAGE_KEY);
    if (saved) {
        try {
            workspaces = JSON.parse(saved);
        } catch (error) {
            console.error('Error loading workspaces:', error);
            workspaces = ['default'];
        }
    }
    return workspaces;
}

function saveWorkspaces() {
    localStorage.setItem(WORKSPACES_STORAGE_KEY, JSON.stringify(workspaces));
}

function loadFavorites() {
    const saved = localStorage.getItem(FAVORITES_STORAGE_KEY);
    if (saved) {
        try {
            favorites = JSON.parse(saved);
        } catch (error) {
            console.error('Error loading favorites:', error);
            favorites = { documents: [], prompts: [], settings: {} };
        }
    }
    return favorites;
}

function saveFavorites() {
    localStorage.setItem(FAVORITES_STORAGE_KEY, JSON.stringify(favorites));
}

function openDocumentsModal() {
    loadDocuments();
    loadWorkspaces();
    loadFavorites();

    updateWorkspaceSelect();
    updateTagFilter();
    displayDocuments();

    documentsModal.classList.add('active');
    document.body.style.overflow = 'hidden';
}

function closeDocumentsModal() {
    documentsModal.classList.remove('active');
    document.body.style.overflow = '';
    selectedDocuments = [];
    updateDeleteButton();
}

function openSaveDocumentModal() {
    const originalText = inputText.value.trim();
    const refinedText = outputText.value.trim();

    if (!refinedText) {
        alert('No refined text to save. Please refine some text first.');
        return;
    }

    // Generate default title from first few words
    const defaultTitle = generateDefaultTitle(refinedText);
    documentTitle.value = defaultTitle;
    documentDescription.value = '';
    documentTags.value = '';

    // Update workspace options
    updateDocumentWorkspaceSelect();

    saveDocumentModal.classList.add('active');
    document.body.style.overflow = 'hidden';

    // Focus on title input
    setTimeout(() => documentTitle.focus(), 100);
}

function closeSaveDocumentModal() {
    saveDocumentModal.classList.remove('active');
    document.body.style.overflow = '';
}

function generateDefaultTitle(text) {
    const words = text.split(/\s+/).slice(0, 6);
    let title = words.join(' ');
    if (title.length > 50) {
        title = title.substring(0, 47) + '...';
    }
    return title || 'Untitled Document';
}

function updateWorkspaceSelect() {
    workspaceSelect.innerHTML = '<option value="all">All Documents</option>';
    workspaces.forEach(workspace => {
        const option = document.createElement('option');
        option.value = workspace;
        option.textContent = workspace.charAt(0).toUpperCase() + workspace.slice(1);
        workspaceSelect.appendChild(option);
    });
}

function updateDocumentWorkspaceSelect() {
    documentWorkspace.innerHTML = '';
    workspaces.forEach(workspace => {
        const option = document.createElement('option');
        option.value = workspace;
        option.textContent = workspace.charAt(0).toUpperCase() + workspace.slice(1);
        documentWorkspace.appendChild(option);
    });
}

function updateTagFilter() {
    const allTags = new Set();
    documents.forEach(doc => {
        if (doc.tags) {
            doc.tags.forEach(tag => allTags.add(tag));
        }
    });

    tagFilter.innerHTML = '<option value="">All Tags</option>';
    Array.from(allTags).sort().forEach(tag => {
        const option = document.createElement('option');
        option.value = tag;
        option.textContent = tag;
        tagFilter.appendChild(option);
    });
}

function saveDocument() {
    const title = documentTitle.value.trim();
    const description = documentDescription.value.trim();
    const workspace = documentWorkspace.value;
    const tags = documentTags.value.trim().split(',').map(tag => tag.trim()).filter(tag => tag);
    const isFavorite = saveAsFavorite.checked;
    const includeOriginal = saveOriginalText.checked;
    const includeMetadata = saveMetadata.checked;

    if (!title) {
        alert('Please enter a document title.');
        documentTitle.focus();
        return;
    }

    const originalText = inputText.value.trim();
    const refinedText = outputText.value.trim();

    const document = {
        id: generateDocumentId(),
        title,
        description,
        workspace,
        tags,
        originalText: includeOriginal ? originalText : '',
        refinedText,
        metadata: includeMetadata ? {
            action: actionSelect.value,
            tone: toneSelect.value,
            language: langSelect.value,
            model: modelSelect.value,
            timestamp: Date.now(),
            statistics: currentTextStats
        } : null,
        isFavorite,
        createdAt: Date.now(),
        updatedAt: Date.now()
    };

    documents.unshift(document); // Add to beginning

    // Limit documents
    if (documents.length > MAX_DOCUMENTS) {
        documents = documents.slice(0, MAX_DOCUMENTS);
    }

    saveDocuments();

    // Add to favorites if requested
    if (isFavorite) {
        favorites.documents.push(document.id);
        saveFavorites();
    }

    closeSaveDocumentModal();

    // Show success notification
    showDocumentNotification(`Document "${title}" saved successfully!`);

    // Show save button feedback
    const originalButtonText = confirmSaveDocumentBtn.innerHTML;
    confirmSaveDocumentBtn.innerHTML = '<span class="button-icon">✅</span>Saved!';
    confirmSaveDocumentBtn.disabled = true;

    setTimeout(() => {
        confirmSaveDocumentBtn.innerHTML = originalButtonText;
        confirmSaveDocumentBtn.disabled = false;
    }, 1500);
}

function generateDocumentId() {
    return 'doc_' + Date.now() + '_' + Math.random().toString(36).substring(2, 11);
}

function displayDocuments() {
    const filteredDocs = filterDocuments();

    documentCount.textContent = `${filteredDocs.length} document${filteredDocs.length !== 1 ? 's' : ''}`;

    if (filteredDocs.length === 0) {
        documentsList.innerHTML = '';
        emptyDocumentsMessage.style.display = 'block';
        return;
    }

    emptyDocumentsMessage.style.display = 'none';

    documentsList.innerHTML = '';
    filteredDocs.forEach(doc => {
        const docCard = createDocumentCard(doc);
        documentsList.appendChild(docCard);
    });
}

function filterDocuments() {
    let filtered = [...documents];

    // Filter by workspace
    if (currentFilters.workspace && currentFilters.workspace !== 'all') {
        filtered = filtered.filter(doc => doc.workspace === currentFilters.workspace);
    }

    // Filter by search
    if (currentFilters.search) {
        const searchLower = currentFilters.search.toLowerCase();
        filtered = filtered.filter(doc =>
            doc.title.toLowerCase().includes(searchLower) ||
            doc.description.toLowerCase().includes(searchLower) ||
            doc.refinedText.toLowerCase().includes(searchLower) ||
            (doc.tags && doc.tags.some(tag => tag.toLowerCase().includes(searchLower)))
        );
    }

    // Filter by tags
    if (currentFilters.tags) {
        filtered = filtered.filter(doc =>
            doc.tags && doc.tags.includes(currentFilters.tags)
        );
    }

    // Filter by date
    if (currentFilters.date) {
        const now = Date.now();
        const dayMs = 24 * 60 * 60 * 1000;

        filtered = filtered.filter(doc => {
            const docDate = doc.createdAt;
            switch (currentFilters.date) {
                case 'today':
                    return now - docDate < dayMs;
                case 'week':
                    return now - docDate < 7 * dayMs;
                case 'month':
                    return now - docDate < 30 * dayMs;
                default:
                    return true;
            }
        });
    }

    // Sort by creation date (newest first)
    filtered.sort((a, b) => b.createdAt - a.createdAt);

    return filtered;
}

function createDocumentCard(doc) {
    const card = document.createElement('div');
    card.className = 'document-card';
    card.dataset.documentId = doc.id;

    const preview = doc.refinedText.substring(0, 150) + (doc.refinedText.length > 150 ? '...' : '');
    const createdDate = new Date(doc.createdAt).toLocaleDateString();
    const tagsHtml = doc.tags ? doc.tags.map(tag => `<span class="document-tag">${escapeHtml(tag)}</span>`).join('') : '';

    card.innerHTML = `
        <div class="document-header">
            <h4 class="document-title">${escapeHtml(doc.title)}</h4>
            <div class="document-meta">
                ${doc.isFavorite ? '<span class="document-favorite">⭐</span>' : ''}
                <span class="document-workspace">${escapeHtml(doc.workspace)}</span>
                <span class="document-date">${createdDate}</span>
            </div>
        </div>
        ${doc.description ? `<div class="document-description">${escapeHtml(doc.description)}</div>` : ''}
        ${doc.tags && doc.tags.length > 0 ? `<div class="document-tags">${tagsHtml}</div>` : ''}
        <div class="document-preview">${escapeHtml(preview)}</div>
        <div class="document-actions">
            <button class="document-action-btn edit" onclick="loadDocument('${doc.id}')">
                📝 Load
            </button>
            <button class="document-action-btn favorite" onclick="toggleDocumentFavorite('${doc.id}')">
                ${doc.isFavorite ? '💔 Unfavorite' : '⭐ Favorite'}
            </button>
            <button class="document-action-btn delete" onclick="deleteDocument('${doc.id}')">
                🗑️ Delete
            </button>
        </div>
    `;

    // Add click handler for selection
    card.addEventListener('click', (e) => {
        if (!e.target.classList.contains('document-action-btn')) {
            toggleDocumentSelection(doc.id);
        }
    });

    return card;
}

function toggleDocumentSelection(docId) {
    const card = document.querySelector(`[data-document-id="${docId}"]`);
    if (!card) return;

    if (selectedDocuments.includes(docId)) {
        selectedDocuments = selectedDocuments.filter(id => id !== docId);
        card.classList.remove('selected');
    } else {
        selectedDocuments.push(docId);
        card.classList.add('selected');
    }

    updateDeleteButton();
}

function updateDeleteButton() {
    if (selectedDocuments.length > 0) {
        deleteSelectedBtn.style.display = 'inline-flex';
        deleteSelectedBtn.textContent = `🗑️ Delete Selected (${selectedDocuments.length})`;
    } else {
        deleteSelectedBtn.style.display = 'none';
    }
}

function loadDocument(docId) {
    const doc = documents.find(d => d.id === docId);
    if (!doc) return;

    // Load document content
    if (doc.originalText) {
        inputText.value = doc.originalText;
    }
    outputText.value = doc.refinedText;

    // Load metadata if available
    if (doc.metadata) {
        actionSelect.value = doc.metadata.action || actionSelect.value;
        toneSelect.value = doc.metadata.tone || toneSelect.value;
        langSelect.value = doc.metadata.language || langSelect.value;
        if (doc.metadata.model) {
            modelSelect.value = doc.metadata.model;
        }
    }

    updateCharCount();
    copyBtn.disabled = false;
    compareBtn.style.display = 'inline-flex';
    exportBtn.style.display = 'inline-flex';
    saveDocumentBtn.style.display = 'inline-flex';

    // Close documents modal
    closeDocumentsModal();

    // Show success notification
    showDocumentNotification(`Document "${doc.title}" loaded successfully!`);
}

function toggleDocumentFavorite(docId) {
    const docIndex = documents.findIndex(d => d.id === docId);
    if (docIndex === -1) return;

    documents[docIndex].isFavorite = !documents[docIndex].isFavorite;
    documents[docIndex].updatedAt = Date.now();

    // Update favorites list
    if (documents[docIndex].isFavorite) {
        if (!favorites.documents.includes(docId)) {
            favorites.documents.push(docId);
        }
    } else {
        favorites.documents = favorites.documents.filter(id => id !== docId);
    }

    saveDocuments();
    saveFavorites();
    displayDocuments();

    const action = documents[docIndex].isFavorite ? 'added to' : 'removed from';
    showDocumentNotification(`Document ${action} favorites!`);
}

function deleteDocument(docId) {
    const doc = documents.find(d => d.id === docId);
    if (!doc) return;

    if (!confirm(`Are you sure you want to delete "${doc.title}"?`)) {
        return;
    }

    documents = documents.filter(d => d.id !== docId);
    favorites.documents = favorites.documents.filter(id => id !== docId);
    selectedDocuments = selectedDocuments.filter(id => id !== docId);

    saveDocuments();
    saveFavorites();
    displayDocuments();
    updateDeleteButton();

    showDocumentNotification(`Document "${doc.title}" deleted successfully!`);
}

function deleteSelectedDocuments() {
    if (selectedDocuments.length === 0) return;

    if (!confirm(`Are you sure you want to delete ${selectedDocuments.length} selected document(s)?`)) {
        return;
    }

    documents = documents.filter(d => !selectedDocuments.includes(d.id));
    favorites.documents = favorites.documents.filter(id => !selectedDocuments.includes(id));

    const count = selectedDocuments.length;
    selectedDocuments = [];

    saveDocuments();
    saveFavorites();
    displayDocuments();
    updateDeleteButton();

    showDocumentNotification(`${count} document(s) deleted successfully!`);
}

function showDocumentNotification(message) {
    const notification = document.createElement('div');
    notification.className = 'contextual-notification';
    notification.innerHTML = `
        <div class="notification-content">
            <span class="notification-icon">📚</span>
            <div class="notification-text">
                <div class="notification-title">Document Manager</div>
                <div class="notification-reason">${message}</div>
            </div>
            <button class="notification-close">✕</button>
        </div>
    `;

    document.body.appendChild(notification);

    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 3000);

    notification.querySelector('.notification-close').addEventListener('click', () => {
        notification.remove();
    });
}

// --- 18. Prompts Management Modal Functions ---
function openPromptsModal() {
    promptsModal.classList.add('active');
    document.body.style.overflow = 'hidden';
    loadPromptsInModal();
    clearPromptEditor();
}

function closePromptsModal() {
    promptsModal.classList.remove('active');
    document.body.style.overflow = '';
    currentEditingPrompt = null;
    clearPromptEditor();
}

function loadPromptsInModal() {
    const prompts = loadCustomPrompts();
    promptsList.innerHTML = '';

    if (prompts.length === 0) {
        emptyPromptsMessage.style.display = 'block';
        return;
    }

    emptyPromptsMessage.style.display = 'none';

    prompts.forEach(prompt => {
        const promptItem = createPromptItem(prompt);
        promptsList.appendChild(promptItem);
    });
}

function createPromptItem(prompt) {
    const item = document.createElement('div');
    item.className = 'prompt-item';
    item.dataset.promptId = prompt.id;

    item.innerHTML = `
        <div class="prompt-info">
            <div class="prompt-name">${escapeHtml(prompt.name)}</div>
            <div class="prompt-meta">
                <span class="prompt-category">${escapeHtml(prompt.category)}</span>
                ${prompt.description ? `<span>${escapeHtml(prompt.description)}</span>` : ''}
            </div>
        </div>
        <div class="prompt-actions">
            <button class="prompt-action-btn edit-prompt-btn" title="Edit prompt">✏️</button>
            <button class="prompt-action-btn delete-prompt-action-btn" title="Delete prompt">🗑️</button>
        </div>
    `;

    // Add event listeners
    item.addEventListener('click', (e) => {
        if (!e.target.classList.contains('prompt-action-btn')) {
            selectPromptItem(item, prompt);
        }
    });

    item.querySelector('.edit-prompt-btn').addEventListener('click', (e) => {
        e.stopPropagation();
        editPrompt(prompt);
    });

    item.querySelector('.delete-prompt-action-btn').addEventListener('click', (e) => {
        e.stopPropagation();
        deletePrompt(prompt.id);
    });

    return item;
}

function selectPromptItem(item, prompt) {
    // Remove previous selection
    document.querySelectorAll('.prompt-item.selected').forEach(el => {
        el.classList.remove('selected');
    });

    // Select current item
    item.classList.add('selected');

    // Load prompt into editor
    loadPromptIntoEditor(prompt);
}

function loadPromptIntoEditor(prompt) {
    currentEditingPrompt = prompt;
    promptNameInput.value = prompt.name;
    promptCategorySelect.value = prompt.category;
    promptTextInput.value = prompt.prompt;
    promptDescriptionInput.value = prompt.description || '';

    deletePromptBtn.style.display = 'block';
}

function clearPromptEditor() {
    currentEditingPrompt = null;
    promptNameInput.value = '';
    promptCategorySelect.value = 'general';
    promptTextInput.value = '';
    promptDescriptionInput.value = '';

    deletePromptBtn.style.display = 'none';

    // Clear selection
    document.querySelectorAll('.prompt-item.selected').forEach(el => {
        el.classList.remove('selected');
    });
}

function savePrompt() {
    const name = promptNameInput.value.trim();
    const category = promptCategorySelect.value;
    const promptText = promptTextInput.value.trim();
    const description = promptDescriptionInput.value.trim();

    if (!name) {
        alert('Please enter a prompt name.');
        promptNameInput.focus();
        return;
    }

    if (!promptText) {
        alert('Please enter the prompt text.');
        promptTextInput.focus();
        return;
    }

    const prompts = loadCustomPrompts();

    if (currentEditingPrompt) {
        // Update existing prompt
        const index = prompts.findIndex(p => p.id === currentEditingPrompt.id);
        if (index !== -1) {
            prompts[index] = {
                ...currentEditingPrompt,
                name,
                category,
                prompt: promptText,
                description,
                updatedAt: Date.now()
            };
        }
    } else {
        // Create new prompt
        const newPrompt = {
            id: generatePromptId(),
            name,
            category,
            prompt: promptText,
            description,
            createdAt: Date.now()
        };
        prompts.push(newPrompt);
    }

    saveCustomPrompts(prompts);
    loadPromptsInModal();
    updateSavedPromptsSelect();
    clearPromptEditor();

    // Show success feedback
    const originalButtonHTML = savePromptBtn.innerHTML;
    savePromptBtn.innerHTML = '<span class="button-icon">✅</span>Saved!';
    savePromptBtn.disabled = true;

    setTimeout(() => {
        savePromptBtn.innerHTML = originalButtonHTML;
        savePromptBtn.disabled = false;
    }, 1500);
}

function editPrompt(prompt) {
    loadPromptIntoEditor(prompt);
}

function deletePrompt(promptId) {
    if (!confirm('Are you sure you want to delete this prompt?')) {
        return;
    }

    const prompts = loadCustomPrompts();
    const filteredPrompts = prompts.filter(p => p.id !== promptId);

    saveCustomPrompts(filteredPrompts);
    loadPromptsInModal();
    updateSavedPromptsSelect();
    clearPromptEditor();
}

function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

// --- 12. Initialize the application ---
document.addEventListener('DOMContentLoaded', initializeUI);