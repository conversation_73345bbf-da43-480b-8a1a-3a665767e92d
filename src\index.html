<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Smart Scribe - AI Writing Assistant</title>
  <link rel="stylesheet" href="index.css">
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>

<body>
  <div class="app-container">
    <!-- Header -->
    <header class="app-header">
      <div class="header-content">
        <div class="logo-section">
          <div class="logo-icon">✍️</div>
          <h1 class="app-title">Smart Scribe</h1>
          <span class="app-subtitle">AI Writing Assistant</span>
        </div>
        <div class="status-indicator">
          <div class="status-dot"></div>
          <span class="status-text">Ready</span>
        </div>
      </div>
    </header>

    <!-- Main Content -->
    <main class="main-content">
      <!-- Quick Controls Panel -->
      <section class="quick-controls-section">
        <div class="quick-controls-panel">
          <div class="quick-controls-left">
            <div class="control-group">
              <label for="action-select" class="control-label">Action</label>
              <select id="action-select" class="control-select">
                <option value="improve-style">Improve Style</option>
                <option value="fix-grammar">Fix Grammar</option>
                <option value="shorten">Shorten</option>
                <option value="expand">Expand</option>
                <option value="custom-prompt">Custom Prompt</option>
              </select>
            </div>

            <div class="control-group">
              <label for="tone-select" class="control-label">Tone</label>
              <select id="tone-select" class="control-select">
                <option value="professional">Professional</option>
                <option value="casual">Casual</option>
                <option value="confident">Confident</option>
                <option value="friendly">Friendly</option>
              </select>
            </div>

            <div class="control-group">
              <label for="lang-select" class="control-label">Language</label>
              <select id="lang-select" class="control-select">
                <option value="English">English</option>
                <option value="French">French</option>
                <option value="Spanish">Spanish</option>
                <option value="German">German</option>
              </select>
            </div>
          </div>

          <div class="quick-controls-right">
            <button id="settings-btn" class="settings-button" title="Settings">
              <span class="settings-icon">⚙</span>
            </button>

            <button id="refine-btn" class="refine-button">
              <span class="button-icon">✨</span>
              <span class="button-text">Refine Text</span>
              <div class="button-loader"></div>
            </button>

            <button id="batch-process-btn" class="batch-button" title="Process multiple texts">
              <span class="button-icon">⚡</span>
              <span class="button-text">Batch Process</span>
            </button>

            <button id="templates-btn" class="templates-button" title="Use writing templates">
              <span class="button-icon">📋</span>
              <span class="button-text">Templates</span>
            </button>

            <button id="documents-btn" class="documents-button" title="Manage documents">
              <span class="button-icon">📚</span>
              <span class="button-text">Documents</span>
            </button>
          </div>
        </div>
      </section>

      <!-- Custom Prompts Section -->
      <section id="custom-prompts-section" class="custom-prompts-section" style="display: none;">
        <div class="custom-prompts-panel">
          <div class="custom-prompts-header">
            <h3 class="section-title">
              <span class="section-icon">📝</span>
              Custom Prompts
            </h3>
            <div class="custom-prompts-actions">
              <button id="new-prompt-btn" class="action-button primary-button" title="Create new custom prompt">
                <span>➕</span>
                New Prompt
              </button>
              <button id="manage-prompts-btn" class="action-button secondary-button" title="Manage saved prompts">
                <span>⚙️</span>
                Manage
              </button>
            </div>
          </div>

          <div class="custom-prompt-editor">
            <div class="prompt-selector">
              <label for="saved-prompts-select" class="control-label">Saved Prompts</label>
              <select id="saved-prompts-select" class="control-select">
                <option value="">Select a saved prompt...</option>
              </select>
            </div>

            <div class="prompt-input-group">
              <label for="custom-prompt-input" class="control-label">Custom Prompt</label>
              <textarea id="custom-prompt-input" class="custom-prompt-textarea"
                placeholder="Enter your custom prompt here. Use {text} as a placeholder for the input text.&#10;&#10;Example: Please rewrite the following text to be more {tone} and suitable for {language}: {text}"
                rows="4"></textarea>
              <div class="prompt-variables">
                <span class="variable-hint">Available variables: {text}, {language}, {tone}</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- Text Panels -->
      <section class="text-section">
        <div class="text-panels">
          <div class="text-panel input-panel">
            <div class="panel-header">
              <h2 class="panel-title">
                <span class="panel-icon">📝</span>
                Input
              </h2>
              <div class="panel-actions">
                <div class="history-controls">
                  <button id="undo-btn" class="action-button history-button" title="Undo last change" disabled>
                    <span class="history-icon">↶</span>
                  </button>
                  <button id="redo-btn" class="action-button history-button" title="Redo last change" disabled>
                    <span class="history-icon">↷</span>
                  </button>
                </div>
                <button id="import-text-btn" class="action-button import-button" title="Import text from file">
                  <span class="import-icon">📁</span>
                  <span class="import-text">Import</span>
                </button>
                <button id="clear-btn" class="action-button" title="Clear text">
                  <span>Clear</span>
                </button>
                <div class="char-counter">
                  <span id="char-count">0</span> characters
                </div>
              </div>
            </div>
            <div class="textarea-container">
              <textarea id="input-text" placeholder="Type or paste your text here..." spellcheck="true"
                aria-label="Input text for refinement"></textarea>

              <!-- Drag and Drop Area -->
              <div id="drag-drop-area" class="drag-drop-area" style="display: none;">
                <div class="drag-drop-content">
                  <div class="drag-drop-icon">📁</div>
                  <div class="drag-drop-text">Drop your file here</div>
                  <div class="drag-drop-hint">Supports .txt, .md, .html, .doc, .docx files</div>
                </div>
              </div>
            </div>
          </div>

          <div class="text-panel output-panel">
            <div class="panel-header">
              <h2 class="panel-title">
                <span class="panel-icon">🤖</span>
                <span id="ai-suggestion-title">Output</span>
              </h2>
              <div class="panel-actions">
                <button id="stats-btn" class="action-button stats-button" title="Show text statistics">
                  <span class="stats-icon">📊</span>
                  <span class="stats-text">Stats</span>
                </button>
                <button id="save-document-btn" class="action-button save-document-button" title="Save document"
                  style="display: none;">
                  <span class="save-icon">💾</span>
                  <span class="save-text">Save</span>
                </button>
                <button id="export-btn" class="action-button export-button" title="Export text" style="display: none;">
                  <span class="export-icon">📤</span>
                  <span class="export-text">Export</span>
                </button>
                <button id="copy-btn" class="action-button copy-button" title="Copy to clipboard">
                  <span class="copy-icon">📋</span>
                  <span class="copy-text">Copy</span>
                </button>
                <button id="compare-btn" class="action-button compare-button" title="Compare texts"
                  style="display: none;">
                  <span class="compare-icon">🔍</span>
                  <span class="compare-text">Compare</span>
                </button>
              </div>
            </div>
            <div class="textarea-container">
              <textarea id="output-text" readonly placeholder="AI refined text will appear here..."
                aria-label="Refined text output"></textarea>
              <div class="loading-overlay">
                <div class="loading-spinner"></div>
                <p id="loading-text" class="loading-text">AI is thinking...</p>
              </div>
            </div>
          </div>
        </div>
      </section>
    </main>
  </div>

  <!-- Settings Modal -->
  <div id="settings-modal" class="settings-modal">
    <div class="settings-modal-content">
      <div class="settings-header">
        <h2 class="settings-title">
          <span class="settings-title-icon">⚙️</span>
          Settings
        </h2>
        <button id="close-settings-btn" class="close-settings-btn" title="Close settings">
          ✕
        </button>
      </div>

      <div class="settings-body">
        <!-- AI Provider Settings -->
        <div class="settings-section">
          <h3 class="settings-section-title">
            <span class="section-icon">🤖</span>
            AI Provider
          </h3>

          <div class="settings-grid">
            <div class="setting-item">
              <label for="provider-select" class="setting-label">
                <span class="label-icon">🔌</span>
                Provider
              </label>
              <select id="provider-select" class="setting-select">
                <option value="ollama">Ollama (Local)</option>
                <option value="openrouter">OpenRouter (Cloud)</option>
              </select>
              <small class="setting-description">Choose between local Ollama or cloud OpenRouter</small>
            </div>

            <div class="setting-item">
              <label for="model-select" class="setting-label">
                <span class="label-icon">🧠</span>
                Model
              </label>
              <div class="model-select-container">
                <select id="model-select" class="setting-select">
                  <option value="">Loading models...</option>
                </select>
                <button type="button" id="refresh-models-btn" class="refresh-models-btn" title="Refresh models">
                  🔄
                </button>
                <button type="button" id="recommend-model-btn" class="recommend-model-btn"
                  title="Get AI model recommendation">
                  🎯
                </button>
              </div>
              <div class="model-actions">
                <button type="button" id="set-default-model-btn" class="set-default-btn" title="Set as default model">
                  <span class="button-icon">⭐</span>
                  <span class="button-text">Set as Default</span>
                </button>
                <span id="default-model-indicator" class="default-indicator" style="display: none;">
                  <span class="indicator-icon">⭐</span>
                  <span class="indicator-text">Default Model</span>
                </span>
              </div>

              <!-- Model Recommendation Display -->
              <div id="model-recommendation" class="model-recommendation" style="display: none;">
                <div class="recommendation-header">
                  <span class="recommendation-icon">🎯</span>
                  <span class="recommendation-title">AI Model Recommendation</span>
                  <button id="close-recommendation" class="close-recommendation-btn">✕</button>
                </div>
                <div class="recommendation-content">
                  <div class="recommended-model">
                    <div class="recommended-model-name" id="recommended-model-name"></div>
                    <div class="recommended-model-reason" id="recommended-model-reason"></div>
                  </div>
                  <div class="recommendation-details">
                    <div class="text-analysis" id="text-analysis"></div>
                    <div class="model-benefits" id="model-benefits"></div>
                  </div>
                  <div class="recommendation-actions">
                    <button id="apply-recommendation-btn" class="apply-recommendation-btn">
                      <span class="button-icon">✅</span>
                      Apply Recommendation
                    </button>
                    <button id="dismiss-recommendation-btn" class="dismiss-recommendation-btn">
                      <span class="button-icon">❌</span>
                      Dismiss
                    </button>
                  </div>
                </div>
              </div>

              <small class="setting-description">Select the AI model to use for text refinement</small>
            </div>
          </div>
        </div>

        <!-- OpenRouter API Settings -->
        <div id="openrouter-settings" class="settings-section" style="display: none;">
          <h3 class="settings-section-title">
            <span class="section-icon">🔑</span>
            OpenRouter API
          </h3>

          <div class="setting-item">
            <label for="api-key-input" class="setting-label">
              <span class="label-icon">🔐</span>
              API Key
            </label>
            <div class="api-key-container">
              <input type="password" id="api-key-input" class="setting-input"
                placeholder="Enter your OpenRouter API key..." autocomplete="off" />
              <button type="button" id="toggle-key-visibility" class="toggle-visibility-btn" title="Toggle visibility">
                👁️
              </button>
            </div>
            <small class="setting-description">
              Get your API key from <a href="https://openrouter.ai/keys" target="_blank" rel="noopener"
                class="setting-link">OpenRouter Dashboard</a>
            </small>
          </div>
        </div>

        <!-- Default Preferences -->
        <div class="settings-section">
          <h3 class="settings-section-title">
            <span class="section-icon">⚙️</span>
            Default Preferences
          </h3>

          <div class="settings-grid">
            <div class="setting-item">
              <label for="default-action-select" class="setting-label">
                <span class="label-icon">✨</span>
                Default Action
              </label>
              <select id="default-action-select" class="setting-select">
                <option value="improve-style">Improve Style</option>
                <option value="fix-grammar">Fix Grammar</option>
                <option value="shorten">Shorten</option>
                <option value="expand">Expand</option>
              </select>
              <small class="setting-description">Default action when opening the app</small>
            </div>

            <div class="setting-item">
              <label for="default-tone-select" class="setting-label">
                <span class="label-icon">🎭</span>
                Default Tone
              </label>
              <select id="default-tone-select" class="setting-select">
                <option value="professional">Professional</option>
                <option value="casual">Casual</option>
                <option value="confident">Confident</option>
                <option value="friendly">Friendly</option>
              </select>
              <small class="setting-description">Default tone for text refinement</small>
            </div>

            <div class="setting-item">
              <label for="default-language-select" class="setting-label">
                <span class="label-icon">🌐</span>
                Default Language
              </label>
              <select id="default-language-select" class="setting-select">
                <option value="English">English</option>
                <option value="French">French</option>
                <option value="Spanish">Spanish</option>
                <option value="German">German</option>
              </select>
              <small class="setting-description">Default language for text processing</small>
            </div>
          </div>
        </div>

        <!-- Application Settings -->
        <div class="settings-section">
          <h3 class="settings-section-title">
            <span class="section-icon">🎛️</span>
            Application
          </h3>

          <div class="settings-grid">
            <div class="setting-item">
              <label for="auto-save-settings" class="setting-label">
                <span class="label-icon">💾</span>
                Auto-save Settings
              </label>
              <label class="toggle-switch">
                <input type="checkbox" id="auto-save-settings" checked>
                <span class="toggle-slider"></span>
              </label>
              <small class="setting-description">Automatically save your preferences</small>
            </div>

            <div class="setting-item">
              <label for="show-char-count" class="setting-label">
                <span class="label-icon">🔢</span>
                Show Character Count
              </label>
              <label class="toggle-switch">
                <input type="checkbox" id="show-char-count" checked>
                <span class="toggle-slider"></span>
              </label>
              <small class="setting-description">Display character count in text panels</small>
            </div>

            <div class="setting-item">
              <label for="remember-last-text" class="setting-label">
                <span class="label-icon">📝</span>
                Remember Last Text
              </label>
              <label class="toggle-switch">
                <input type="checkbox" id="remember-last-text">
                <span class="toggle-slider"></span>
              </label>
              <small class="setting-description">Save and restore your last input text</small>
            </div>
          </div>
        </div>
      </div>

      <div class="settings-footer">
        <button id="reset-settings-btn" class="reset-settings-btn">
          <span class="button-icon">🔄</span>
          Reset to Defaults
        </button>
        <button id="save-settings-btn" class="save-settings-btn">
          <span class="button-icon">💾</span>
          Save Settings
        </button>
      </div>
    </div>
  </div>

  <!-- Custom Prompts Management Modal -->
  <div id="prompts-modal" class="settings-modal">
    <div class="settings-modal-content">
      <div class="settings-header">
        <h2 class="settings-title">
          <span class="settings-title-icon">📝</span>
          Manage Custom Prompts
        </h2>
        <button id="close-prompts-btn" class="close-settings-btn" title="Close prompts manager">
          ✕
        </button>
      </div>

      <div class="settings-body">
        <!-- Prompt List -->
        <div class="settings-section">
          <h3 class="settings-section-title">
            <span class="section-icon">📋</span>
            Saved Prompts
          </h3>

          <div class="prompts-list" id="prompts-list">
            <div class="empty-prompts-message" id="empty-prompts-message">
              <span class="empty-icon">📝</span>
              <p>No custom prompts saved yet.</p>
              <p>Create your first custom prompt to get started!</p>
            </div>
          </div>
        </div>

        <!-- Prompt Editor -->
        <div class="settings-section">
          <h3 class="settings-section-title">
            <span class="section-icon">✏️</span>
            Prompt Editor
          </h3>

          <div class="prompt-editor-form">
            <div class="setting-item">
              <label for="prompt-name-input" class="setting-label">
                <span class="label-icon">🏷️</span>
                Prompt Name
              </label>
              <input type="text" id="prompt-name-input" class="setting-input"
                placeholder="Enter a name for your prompt..." maxlength="50">
            </div>

            <div class="setting-item">
              <label for="prompt-category-select" class="setting-label">
                <span class="label-icon">📁</span>
                Category
              </label>
              <select id="prompt-category-select" class="setting-select">
                <option value="general">General</option>
                <option value="business">Business</option>
                <option value="academic">Academic</option>
                <option value="creative">Creative</option>
                <option value="technical">Technical</option>
                <option value="social">Social Media</option>
              </select>
            </div>

            <div class="setting-item">
              <label for="prompt-text-input" class="setting-label">
                <span class="label-icon">📝</span>
                Prompt Text
              </label>
              <textarea id="prompt-text-input" class="setting-textarea"
                placeholder="Enter your custom prompt here. Use {text} for the input text, {language} for language, and {tone} for tone."
                rows="6"></textarea>
              <small class="setting-description">
                Available variables: {text}, {language}, {tone}
              </small>
            </div>

            <div class="setting-item">
              <label for="prompt-description-input" class="setting-label">
                <span class="label-icon">📄</span>
                Description (Optional)
              </label>
              <input type="text" id="prompt-description-input" class="setting-input"
                placeholder="Brief description of what this prompt does..." maxlength="100">
            </div>
          </div>
        </div>
      </div>

      <div class="settings-footer">
        <button id="delete-prompt-btn" class="delete-prompt-btn" style="display: none;">
          <span class="button-icon">🗑️</span>
          Delete Prompt
        </button>
        <div class="footer-right">
          <button id="cancel-prompt-btn" class="cancel-prompt-btn">
            <span class="button-icon">❌</span>
            Cancel
          </button>
          <button id="save-prompt-btn" class="save-prompt-btn">
            <span class="button-icon">💾</span>
            Save Prompt
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Batch Processing Modal -->
  <div id="batch-modal" class="settings-modal">
    <div class="settings-modal-content batch-modal-content">
      <div class="settings-header">
        <h2 class="settings-title">
          <span class="settings-title-icon">⚡</span>
          Batch Processing
        </h2>
        <button id="close-batch-btn" class="close-settings-btn" title="Close batch processor">
          ✕
        </button>
      </div>

      <div class="settings-body">
        <!-- Input Section -->
        <div class="settings-section">
          <h3 class="settings-section-title">
            <span class="section-icon">📝</span>
            Input Texts
          </h3>

          <div class="batch-input-section">
            <div class="batch-input-controls">
              <button id="add-text-btn" class="action-button primary-button">
                <span>➕</span>
                Add Text
              </button>
              <button id="import-texts-btn" class="action-button secondary-button">
                <span>📁</span>
                Import from File
              </button>
              <button id="clear-all-texts-btn" class="action-button secondary-button">
                <span>🗑️</span>
                Clear All
              </button>
            </div>

            <div class="batch-texts-list" id="batch-texts-list">
              <div class="empty-batch-message" id="empty-batch-message">
                <span class="empty-icon">📝</span>
                <p>No texts added yet.</p>
                <p>Add texts to process them in batch!</p>
              </div>
            </div>
          </div>
        </div>

        <!-- Processing Settings -->
        <div class="settings-section">
          <h3 class="settings-section-title">
            <span class="section-icon">⚙️</span>
            Processing Settings
          </h3>

          <div class="batch-settings-grid">
            <div class="setting-item">
              <label for="batch-action-select" class="setting-label">
                <span class="label-icon">✨</span>
                Action
              </label>
              <select id="batch-action-select" class="setting-select">
                <option value="improve-style">Improve Style</option>
                <option value="fix-grammar">Fix Grammar</option>
                <option value="shorten">Shorten</option>
                <option value="expand">Expand</option>
                <option value="custom-prompt">Custom Prompt</option>
              </select>
            </div>

            <div class="setting-item">
              <label for="batch-tone-select" class="setting-label">
                <span class="label-icon">🎭</span>
                Tone
              </label>
              <select id="batch-tone-select" class="setting-select">
                <option value="professional">Professional</option>
                <option value="casual">Casual</option>
                <option value="confident">Confident</option>
                <option value="friendly">Friendly</option>
              </select>
            </div>

            <div class="setting-item">
              <label for="batch-language-select" class="setting-label">
                <span class="label-icon">🌐</span>
                Language
              </label>
              <select id="batch-language-select" class="setting-select">
                <option value="English">English</option>
                <option value="French">French</option>
                <option value="Spanish">Spanish</option>
                <option value="German">German</option>
              </select>
            </div>

            <div class="setting-item">
              <label for="batch-concurrent-select" class="setting-label">
                <span class="label-icon">🔄</span>
                Concurrent Processing
              </label>
              <select id="batch-concurrent-select" class="setting-select">
                <option value="1">1 at a time</option>
                <option value="2">2 at a time</option>
                <option value="3" selected>3 at a time</option>
                <option value="5">5 at a time</option>
              </select>
              <small class="setting-description">Number of texts to process simultaneously</small>
            </div>
          </div>

          <!-- Custom Prompt for Batch -->
          <div id="batch-custom-prompt-section" class="batch-custom-prompt-section" style="display: none;">
            <div class="setting-item">
              <label for="batch-custom-prompt-select" class="setting-label">
                <span class="label-icon">📝</span>
                Custom Prompt
              </label>
              <select id="batch-custom-prompt-select" class="setting-select">
                <option value="">Select a saved prompt...</option>
              </select>
            </div>

            <div class="setting-item">
              <label for="batch-custom-prompt-input" class="setting-label">
                <span class="label-icon">✏️</span>
                Prompt Text
              </label>
              <textarea id="batch-custom-prompt-input" class="setting-textarea"
                placeholder="Enter custom prompt or select from saved prompts above..." rows="3"></textarea>
            </div>
          </div>
        </div>

        <!-- Progress Section -->
        <div class="settings-section">
          <h3 class="settings-section-title">
            <span class="section-icon">📊</span>
            Progress
          </h3>

          <div class="batch-progress-section">
            <div class="progress-stats">
              <div class="stat-item">
                <span class="stat-label">Total:</span>
                <span id="batch-total-count" class="stat-value">0</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">Processed:</span>
                <span id="batch-processed-count" class="stat-value">0</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">Failed:</span>
                <span id="batch-failed-count" class="stat-value">0</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">Remaining:</span>
                <span id="batch-remaining-count" class="stat-value">0</span>
              </div>
            </div>

            <div class="progress-bar-container">
              <div class="progress-bar">
                <div id="batch-progress-bar" class="progress-fill"></div>
              </div>
              <span id="batch-progress-text" class="progress-text">0%</span>
            </div>

            <div id="batch-current-status" class="batch-status">Ready to process</div>
          </div>
        </div>
      </div>

      <div class="settings-footer">
        <button id="export-results-btn" class="export-results-btn" style="display: none;">
          <span class="button-icon">💾</span>
          Export Results
        </button>
        <div class="footer-right">
          <button id="cancel-batch-btn" class="cancel-batch-btn">
            <span class="button-icon">❌</span>
            Cancel
          </button>
          <button id="start-batch-btn" class="start-batch-btn">
            <span class="button-icon">▶️</span>
            Start Processing
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Text Comparison Modal -->
  <div id="comparison-modal" class="settings-modal">
    <div class="settings-modal-content comparison-modal-content">
      <div class="settings-header">
        <h2 class="settings-title">
          <span class="settings-title-icon">🔍</span>
          Text Comparison
        </h2>
        <button id="close-comparison-btn" class="close-settings-btn" title="Close comparison">
          ✕
        </button>
      </div>

      <div class="settings-body">
        <!-- Comparison Controls -->
        <div class="settings-section">
          <h3 class="settings-section-title">
            <span class="section-icon">⚙️</span>
            Comparison Options
          </h3>

          <div class="comparison-controls">
            <div class="comparison-control-group">
              <label class="comparison-toggle">
                <input type="checkbox" id="show-word-diff" checked>
                <span class="toggle-slider"></span>
                <span class="toggle-label">Word-level differences</span>
              </label>
            </div>

            <div class="comparison-control-group">
              <label class="comparison-toggle">
                <input type="checkbox" id="show-char-diff">
                <span class="toggle-slider"></span>
                <span class="toggle-label">Character-level differences</span>
              </label>
            </div>

            <div class="comparison-control-group">
              <label class="comparison-toggle">
                <input type="checkbox" id="ignore-whitespace">
                <span class="toggle-slider"></span>
                <span class="toggle-label">Ignore whitespace changes</span>
              </label>
            </div>

            <div class="comparison-control-group">
              <label class="comparison-toggle">
                <input type="checkbox" id="ignore-case">
                <span class="toggle-slider"></span>
                <span class="toggle-label">Ignore case differences</span>
              </label>
            </div>
          </div>
        </div>

        <!-- Statistics -->
        <div class="settings-section">
          <h3 class="settings-section-title">
            <span class="section-icon">📊</span>
            Statistics
          </h3>

          <div class="comparison-stats">
            <div class="stat-card">
              <div class="stat-value" id="original-word-count">0</div>
              <div class="stat-label">Original Words</div>
            </div>
            <div class="stat-card">
              <div class="stat-value" id="refined-word-count">0</div>
              <div class="stat-label">Refined Words</div>
            </div>
            <div class="stat-card">
              <div class="stat-value" id="words-changed">0</div>
              <div class="stat-label">Words Changed</div>
            </div>
            <div class="stat-card">
              <div class="stat-value" id="change-percentage">0%</div>
              <div class="stat-label">Change %</div>
            </div>
          </div>
        </div>

        <!-- Side-by-side Comparison -->
        <div class="settings-section">
          <h3 class="settings-section-title">
            <span class="section-icon">📝</span>
            Side-by-side Comparison
          </h3>

          <div class="comparison-panels">
            <div class="comparison-panel">
              <div class="comparison-panel-header">
                <h4 class="comparison-panel-title">
                  <span class="panel-icon">📝</span>
                  Original Text
                </h4>
                <div class="comparison-panel-actions">
                  <button id="copy-original-btn" class="comparison-action-btn" title="Copy original text">
                    📋
                  </button>
                </div>
              </div>
              <div class="comparison-panel-content">
                <div id="original-text-comparison" class="comparison-text"></div>
              </div>
            </div>

            <div class="comparison-panel">
              <div class="comparison-panel-header">
                <h4 class="comparison-panel-title">
                  <span class="panel-icon">🤖</span>
                  Refined Text
                </h4>
                <div class="comparison-panel-actions">
                  <button id="copy-refined-btn" class="comparison-action-btn" title="Copy refined text">
                    📋
                  </button>
                </div>
              </div>
              <div class="comparison-panel-content">
                <div id="refined-text-comparison" class="comparison-text"></div>
              </div>
            </div>
          </div>
        </div>

        <!-- Unified Diff View -->
        <div class="settings-section">
          <h3 class="settings-section-title">
            <span class="section-icon">📄</span>
            Unified Diff View
          </h3>

          <div class="unified-diff-container">
            <div id="unified-diff" class="unified-diff"></div>
          </div>
        </div>
      </div>

      <div class="settings-footer">
        <button id="export-comparison-btn" class="export-comparison-btn">
          <span class="button-icon">💾</span>
          Export Comparison
        </button>
        <div class="footer-right">
          <button id="close-comparison-footer-btn" class="cancel-batch-btn">
            <span class="button-icon">❌</span>
            Close
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Templates Modal -->
  <div id="templates-modal" class="settings-modal">
    <div class="settings-modal-content templates-modal-content">
      <div class="settings-header">
        <h2 class="settings-title">
          <span class="settings-title-icon">📋</span>
          Writing Templates
        </h2>
        <button id="close-templates-btn" class="close-settings-btn" title="Close templates">
          ✕
        </button>
      </div>

      <div class="settings-body">
        <!-- Template Categories -->
        <div class="settings-section">
          <h3 class="settings-section-title">
            <span class="section-icon">📁</span>
            Template Categories
          </h3>

          <div class="template-categories">
            <button class="template-category-btn active" data-category="all">
              <span class="category-icon">📋</span>
              <span class="category-name">All Templates</span>
              <span class="category-count" id="all-count">0</span>
            </button>
            <button class="template-category-btn" data-category="business">
              <span class="category-icon">💼</span>
              <span class="category-name">Business</span>
              <span class="category-count" id="business-count">0</span>
            </button>
            <button class="template-category-btn" data-category="academic">
              <span class="category-icon">🎓</span>
              <span class="category-name">Academic</span>
              <span class="category-count" id="academic-count">0</span>
            </button>
            <button class="template-category-btn" data-category="social">
              <span class="category-icon">📱</span>
              <span class="category-name">Social Media</span>
              <span class="category-count" id="social-count">0</span>
            </button>
            <button class="template-category-btn" data-category="creative">
              <span class="category-icon">🎨</span>
              <span class="category-name">Creative</span>
              <span class="category-count" id="creative-count">0</span>
            </button>
            <button class="template-category-btn" data-category="personal">
              <span class="category-icon">👤</span>
              <span class="category-name">Personal</span>
              <span class="category-count" id="personal-count">0</span>
            </button>
          </div>
        </div>

        <!-- Template Grid -->
        <div class="settings-section">
          <h3 class="settings-section-title">
            <span class="section-icon">📝</span>
            Available Templates
          </h3>

          <div class="template-search">
            <input type="text" id="template-search-input" class="template-search-input"
              placeholder="Search templates..." />
            <span class="search-icon">🔍</span>
          </div>

          <div class="templates-grid" id="templates-grid">
            <!-- Templates will be populated here -->
          </div>
        </div>

        <!-- Template Preview -->
        <div class="settings-section" id="template-preview-section" style="display: none;">
          <h3 class="settings-section-title">
            <span class="section-icon">👁️</span>
            Template Preview
          </h3>

          <div class="template-preview">
            <div class="template-preview-header">
              <div class="template-preview-info">
                <h4 class="template-preview-title" id="preview-title"></h4>
                <p class="template-preview-description" id="preview-description"></p>
                <div class="template-preview-meta">
                  <span class="template-category-tag" id="preview-category"></span>
                  <span class="template-variables-info" id="preview-variables"></span>
                </div>
              </div>
            </div>

            <div class="template-preview-content">
              <div class="template-preview-text" id="preview-content"></div>
            </div>

            <!-- Variable Inputs -->
            <div class="template-variables" id="template-variables" style="display: none;">
              <h5 class="variables-title">Customize Template Variables</h5>
              <div class="variables-grid" id="variables-grid">
                <!-- Variable inputs will be populated here -->
              </div>
            </div>

            <div class="template-preview-actions">
              <button id="use-template-btn" class="use-template-btn">
                <span class="button-icon">✅</span>
                Use This Template
              </button>
              <button id="customize-template-btn" class="customize-template-btn">
                <span class="button-icon">⚙️</span>
                Customize Variables
              </button>
            </div>
          </div>
        </div>
      </div>

      <div class="settings-footer">
        <button id="create-template-btn" class="create-template-btn">
          <span class="button-icon">➕</span>
          Create Custom Template
        </button>
        <div class="footer-right">
          <button id="close-templates-footer-btn" class="cancel-batch-btn">
            <span class="button-icon">❌</span>
            Close
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Create Custom Template Modal -->
  <div id="create-template-modal" class="settings-modal">
    <div class="settings-modal-content">
      <div class="settings-header">
        <h2 class="settings-title">
          <span class="settings-title-icon">➕</span>
          Create Custom Template
        </h2>
        <button id="close-create-template-btn" class="close-settings-btn" title="Close template creator">
          ✕
        </button>
      </div>

      <div class="settings-body">
        <!-- Template Basic Info -->
        <div class="settings-section">
          <h3 class="settings-section-title">
            <span class="section-icon">📝</span>
            Template Information
          </h3>

          <div class="settings-grid">
            <div class="setting-item">
              <label for="new-template-name" class="setting-label">
                <span class="label-icon">🏷️</span>
                Template Name
              </label>
              <input type="text" id="new-template-name" class="setting-input" placeholder="Enter template name..."
                maxlength="50" required>
            </div>

            <div class="setting-item">
              <label for="new-template-category" class="setting-label">
                <span class="label-icon">📁</span>
                Category
              </label>
              <select id="new-template-category" class="setting-select">
                <option value="business">Business</option>
                <option value="academic">Academic</option>
                <option value="social">Social Media</option>
                <option value="creative">Creative</option>
                <option value="personal">Personal</option>
              </select>
            </div>
          </div>

          <div class="setting-item">
            <label for="new-template-description" class="setting-label">
              <span class="label-icon">📄</span>
              Description
            </label>
            <input type="text" id="new-template-description" class="setting-input"
              placeholder="Brief description of the template..." maxlength="200">
          </div>

          <div class="setting-item">
            <label for="new-template-tags" class="setting-label">
              <span class="label-icon">🏷️</span>
              Tags (Optional)
            </label>
            <input type="text" id="new-template-tags" class="setting-input"
              placeholder="Enter tags separated by commas (e.g., email, formal, business)">
            <small class="setting-description">Tags help with searching and categorization</small>
          </div>
        </div>

        <!-- Template Content -->
        <div class="settings-section">
          <h3 class="settings-section-title">
            <span class="section-icon">✏️</span>
            Template Content
          </h3>

          <div class="setting-item">
            <label for="new-template-content" class="setting-label">
              <span class="label-icon">📝</span>
              Template Text
            </label>
            <textarea id="new-template-content" class="setting-textarea"
              placeholder="Enter your template content here. Use {variable_name} for variables that users can customize.&#10;&#10;Example:&#10;Dear {recipient_name},&#10;&#10;I hope this email finds you well. {main_message}&#10;&#10;Best regards,&#10;{sender_name}"
              rows="10" required></textarea>
            <small class="setting-description">
              Use {variable_name} syntax for customizable parts. Variables will be automatically detected.
            </small>
          </div>
        </div>

        <!-- Detected Variables -->
        <div class="settings-section" id="detected-variables-section" style="display: none;">
          <h3 class="settings-section-title">
            <span class="section-icon">🔧</span>
            Detected Variables
          </h3>

          <div id="detected-variables-list" class="detected-variables-list">
            <!-- Variables will be populated here -->
          </div>
        </div>

        <!-- Template Preview -->
        <div class="settings-section">
          <h3 class="settings-section-title">
            <span class="section-icon">👁️</span>
            Preview
          </h3>

          <div class="template-creator-preview">
            <div id="template-creator-preview-content" class="template-preview-text">
              Enter template content above to see preview...
            </div>
          </div>
        </div>
      </div>

      <div class="settings-footer">
        <button id="clear-template-form-btn" class="reset-settings-btn">
          <span class="button-icon">🔄</span>
          Clear Form
        </button>
        <div class="footer-right">
          <button id="cancel-create-template-btn" class="cancel-prompt-btn">
            <span class="button-icon">❌</span>
            Cancel
          </button>
          <button id="save-new-template-btn" class="save-settings-btn">
            <span class="button-icon">💾</span>
            Save Template
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Text Statistics Modal -->
  <div id="stats-modal" class="settings-modal">
    <div class="settings-modal-content">
      <div class="settings-header">
        <h2 class="settings-title">
          <span class="settings-title-icon">📊</span>
          Text Statistics
        </h2>
        <button id="close-stats-btn" class="close-settings-btn" title="Close statistics">
          ✕
        </button>
      </div>

      <div class="settings-body">
        <!-- Basic Statistics -->
        <div class="settings-section">
          <h3 class="settings-section-title">
            <span class="section-icon">📝</span>
            Basic Statistics
          </h3>

          <div class="stats-grid">
            <div class="stat-card">
              <div class="stat-value" id="stat-characters">0</div>
              <div class="stat-label">Characters</div>
            </div>
            <div class="stat-card">
              <div class="stat-value" id="stat-words">0</div>
              <div class="stat-label">Words</div>
            </div>
            <div class="stat-card">
              <div class="stat-value" id="stat-sentences">0</div>
              <div class="stat-label">Sentences</div>
            </div>
            <div class="stat-card">
              <div class="stat-value" id="stat-paragraphs">0</div>
              <div class="stat-label">Paragraphs</div>
            </div>
          </div>
        </div>

        <!-- Reading Statistics -->
        <div class="settings-section">
          <h3 class="settings-section-title">
            <span class="section-icon">⏱️</span>
            Reading Statistics
          </h3>

          <div class="stats-grid">
            <div class="stat-card">
              <div class="stat-value" id="stat-reading-time">0 min</div>
              <div class="stat-label">Reading Time</div>
            </div>
            <div class="stat-card">
              <div class="stat-value" id="stat-speaking-time">0 min</div>
              <div class="stat-label">Speaking Time</div>
            </div>
            <div class="stat-card">
              <div class="stat-value" id="stat-avg-word-length">0</div>
              <div class="stat-label">Avg Word Length</div>
            </div>
            <div class="stat-card">
              <div class="stat-value" id="stat-avg-sentence-length">0</div>
              <div class="stat-label">Avg Sentence Length</div>
            </div>
          </div>
        </div>

        <!-- Complexity Analysis -->
        <div class="settings-section">
          <h3 class="settings-section-title">
            <span class="section-icon">🧠</span>
            Complexity Analysis
          </h3>

          <div class="complexity-analysis">
            <div class="complexity-item">
              <div class="complexity-label">Reading Level</div>
              <div class="complexity-value" id="complexity-reading-level">-</div>
              <div class="complexity-bar">
                <div class="complexity-fill" id="complexity-reading-bar"></div>
              </div>
            </div>

            <div class="complexity-item">
              <div class="complexity-label">Vocabulary Complexity</div>
              <div class="complexity-value" id="complexity-vocabulary">-</div>
              <div class="complexity-bar">
                <div class="complexity-fill" id="complexity-vocabulary-bar"></div>
              </div>
            </div>

            <div class="complexity-item">
              <div class="complexity-label">Sentence Structure</div>
              <div class="complexity-value" id="complexity-structure">-</div>
              <div class="complexity-bar">
                <div class="complexity-fill" id="complexity-structure-bar"></div>
              </div>
            </div>
          </div>
        </div>

        <!-- Text Analysis -->
        <div class="settings-section">
          <h3 class="settings-section-title">
            <span class="section-icon">🔍</span>
            Text Analysis
          </h3>

          <div class="text-analysis-results">
            <div class="analysis-item">
              <div class="analysis-label">Most Common Words</div>
              <div class="analysis-content" id="analysis-common-words">-</div>
            </div>

            <div class="analysis-item">
              <div class="analysis-label">Text Type</div>
              <div class="analysis-content" id="analysis-text-type">-</div>
            </div>

            <div class="analysis-item">
              <div class="analysis-label">Tone Indicators</div>
              <div class="analysis-content" id="analysis-tone">-</div>
            </div>
          </div>
        </div>
      </div>

      <div class="settings-footer">
        <button id="refresh-stats-btn" class="refresh-stats-btn">
          <span class="button-icon">🔄</span>
          Refresh Statistics
        </button>
        <div class="footer-right">
          <button id="close-stats-footer-btn" class="cancel-batch-btn">
            <span class="button-icon">❌</span>
            Close
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Export Options Modal -->
  <div id="export-modal" class="settings-modal">
    <div class="settings-modal-content">
      <div class="settings-header">
        <h2 class="settings-title">
          <span class="settings-title-icon">💾</span>
          Export Text
        </h2>
        <button id="close-export-btn" class="close-settings-btn" title="Close export options">
          ✕
        </button>
      </div>

      <div class="settings-body">
        <!-- Export Format -->
        <div class="settings-section">
          <h3 class="settings-section-title">
            <span class="section-icon">📄</span>
            Export Format
          </h3>

          <div class="export-formats">
            <div class="format-option" data-format="txt">
              <div class="format-icon">📄</div>
              <div class="format-info">
                <div class="format-name">Plain Text (.txt)</div>
                <div class="format-description">Simple text file without formatting</div>
              </div>
            </div>

            <div class="format-option" data-format="md">
              <div class="format-icon">📝</div>
              <div class="format-info">
                <div class="format-name">Markdown (.md)</div>
                <div class="format-description">Markdown formatted text file</div>
              </div>
            </div>

            <div class="format-option" data-format="html">
              <div class="format-icon">🌐</div>
              <div class="format-info">
                <div class="format-name">HTML (.html)</div>
                <div class="format-description">Web page with formatting</div>
              </div>
            </div>

            <div class="format-option" data-format="pdf">
              <div class="format-icon">📕</div>
              <div class="format-info">
                <div class="format-name">PDF (.pdf)</div>
                <div class="format-description">Portable document format</div>
              </div>
            </div>
          </div>
        </div>

        <!-- Export Options -->
        <div class="settings-section">
          <h3 class="settings-section-title">
            <span class="section-icon">⚙️</span>
            Export Options
          </h3>

          <div class="export-options">
            <div class="export-option">
              <label class="export-toggle">
                <input type="checkbox" id="include-original" checked>
                <span class="toggle-slider"></span>
                <span class="toggle-label">Include Original Text</span>
              </label>
            </div>

            <div class="export-option">
              <label class="export-toggle">
                <input type="checkbox" id="include-refined" checked>
                <span class="toggle-slider"></span>
                <span class="toggle-label">Include Refined Text</span>
              </label>
            </div>

            <div class="export-option">
              <label class="export-toggle">
                <input type="checkbox" id="include-metadata">
                <span class="toggle-slider"></span>
                <span class="toggle-label">Include Metadata</span>
              </label>
            </div>

            <div class="export-option">
              <label class="export-toggle">
                <input type="checkbox" id="include-statistics">
                <span class="toggle-slider"></span>
                <span class="toggle-label">Include Statistics</span>
              </label>
            </div>
          </div>
        </div>

        <!-- File Settings -->
        <div class="settings-section">
          <h3 class="settings-section-title">
            <span class="section-icon">📁</span>
            File Settings
          </h3>

          <div class="setting-item">
            <label for="export-filename" class="setting-label">
              <span class="label-icon">📝</span>
              Filename
            </label>
            <input type="text" id="export-filename" class="setting-input" placeholder="Enter filename..."
              value="smart-scribe-export">
            <small class="setting-description">File extension will be added automatically</small>
          </div>
        </div>
      </div>

      <div class="settings-footer">
        <div class="footer-right">
          <button id="cancel-export-btn" class="cancel-batch-btn">
            <span class="button-icon">❌</span>
            Cancel
          </button>
          <button id="start-export-btn" class="start-export-btn">
            <span class="button-icon">💾</span>
            Export File
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Document Management Modal -->
  <div id="documents-modal" class="settings-modal">
    <div class="settings-modal-content documents-modal-content">
      <div class="settings-header">
        <h2 class="settings-title">
          <span class="settings-title-icon">📚</span>
          Document Manager
        </h2>
        <button id="close-documents-btn" class="close-settings-btn" title="Close document manager">
          ✕
        </button>
      </div>

      <div class="settings-body">
        <!-- Workspace and Filter Controls -->
        <div class="settings-section">
          <h3 class="settings-section-title">
            <span class="section-icon">🗂️</span>
            Workspace & Filters
          </h3>

          <div class="workspace-controls">
            <div class="workspace-selector">
              <label for="workspace-select" class="control-label">
                <span class="label-icon">🗂️</span>
                Workspace
              </label>
              <select id="workspace-select" class="control-select">
                <option value="all">All Documents</option>
                <option value="default">Default</option>
              </select>
              <button id="manage-workspaces-btn" class="manage-btn" title="Manage workspaces">
                <span>⚙️</span>
              </button>
            </div>

            <div class="search-controls">
              <div class="search-input-group">
                <input type="text" id="document-search" class="search-input" placeholder="Search documents..." />
                <span class="search-icon">🔍</span>
              </div>

              <div class="filter-controls">
                <select id="tag-filter" class="filter-select">
                  <option value="">All Tags</option>
                </select>
                <select id="date-filter" class="filter-select">
                  <option value="">All Dates</option>
                  <option value="today">Today</option>
                  <option value="week">This Week</option>
                  <option value="month">This Month</option>
                </select>
                <button id="clear-filters-btn" class="clear-filters-btn" title="Clear all filters">
                  <span>🗑️</span>
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- Quick Actions -->
        <div class="settings-section">
          <h3 class="settings-section-title">
            <span class="section-icon">⚡</span>
            Quick Actions
          </h3>

          <div class="quick-actions">
            <button id="new-document-btn" class="quick-action-btn">
              <span class="action-icon">📄</span>
              <span class="action-text">New Document</span>
            </button>
            <button id="import-document-btn" class="quick-action-btn">
              <span class="action-icon">📁</span>
              <span class="action-text">Import</span>
            </button>
            <button id="bulk-export-btn" class="quick-action-btn">
              <span class="action-icon">📦</span>
              <span class="action-text">Bulk Export</span>
            </button>
            <button id="favorites-btn" class="quick-action-btn">
              <span class="action-icon">⭐</span>
              <span class="action-text">Favorites</span>
            </button>
          </div>
        </div>

        <!-- Documents List -->
        <div class="settings-section">
          <h3 class="settings-section-title">
            <span class="section-icon">📋</span>
            Documents
            <span class="document-count" id="document-count">0 documents</span>
          </h3>

          <div class="documents-list" id="documents-list">
            <div class="empty-documents-message" id="empty-documents-message">
              <span class="empty-icon">📚</span>
              <p>No documents saved yet.</p>
              <p>Start by refining some text and saving your first document!</p>
            </div>
          </div>
        </div>
      </div>

      <div class="settings-footer">
        <div class="footer-left">
          <button id="delete-selected-btn" class="delete-selected-btn" style="display: none;">
            <span class="button-icon">🗑️</span>
            Delete Selected
          </button>
        </div>
        <div class="footer-right">
          <button id="close-documents-footer-btn" class="cancel-batch-btn">
            <span class="button-icon">❌</span>
            Close
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Save Document Modal -->
  <div id="save-document-modal" class="settings-modal">
    <div class="settings-modal-content">
      <div class="settings-header">
        <h2 class="settings-title">
          <span class="settings-title-icon">💾</span>
          Save Document
        </h2>
        <button id="close-save-document-btn" class="close-settings-btn" title="Close save dialog">
          ✕
        </button>
      </div>

      <div class="settings-body">
        <div class="settings-section">
          <h3 class="settings-section-title">
            <span class="section-icon">📝</span>
            Document Information
          </h3>

          <div class="setting-item">
            <label for="document-title" class="setting-label">
              <span class="label-icon">🏷️</span>
              Title
            </label>
            <input type="text" id="document-title" class="setting-input" placeholder="Enter document title..."
              maxlength="100" required>
          </div>

          <div class="setting-item">
            <label for="document-description" class="setting-label">
              <span class="label-icon">📄</span>
              Description (Optional)
            </label>
            <textarea id="document-description" class="setting-textarea"
              placeholder="Brief description of the document..." rows="3" maxlength="500"></textarea>
          </div>

          <div class="settings-grid">
            <div class="setting-item">
              <label for="document-workspace" class="setting-label">
                <span class="label-icon">🗂️</span>
                Workspace
              </label>
              <select id="document-workspace" class="setting-select">
                <option value="default">Default</option>
              </select>
            </div>

            <div class="setting-item">
              <label for="document-tags" class="setting-label">
                <span class="label-icon">🏷️</span>
                Tags
              </label>
              <input type="text" id="document-tags" class="setting-input"
                placeholder="Enter tags separated by commas...">
              <small class="setting-description">e.g., business, email, draft</small>
            </div>
          </div>

          <div class="setting-item">
            <label class="setting-label">
              <span class="label-icon">⭐</span>
              Options
            </label>
            <div class="save-options">
              <label class="save-option">
                <input type="checkbox" id="save-as-favorite" />
                <span class="option-text">Add to Favorites</span>
              </label>
              <label class="save-option">
                <input type="checkbox" id="save-original-text" checked />
                <span class="option-text">Include Original Text</span>
              </label>
              <label class="save-option">
                <input type="checkbox" id="save-metadata" checked />
                <span class="option-text">Include Processing Metadata</span>
              </label>
            </div>
          </div>
        </div>
      </div>

      <div class="settings-footer">
        <div class="footer-right">
          <button id="cancel-save-document-btn" class="cancel-batch-btn">
            <span class="button-icon">❌</span>
            Cancel
          </button>
          <button id="confirm-save-document-btn" class="save-settings-btn">
            <span class="button-icon">💾</span>
            Save Document
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Hidden file input for import -->
  <input type="file" id="file-input" accept=".txt,.md,.html,.doc,.docx" style="display: none;">

  <script src="renderer.js"></script>
</body>

</html>